{"name": "step1-mcp", "version": "1.0.0", "description": "MCP package for multi-step user interaction and problem solving", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["mcp", "model-context-protocol", "problem-solving", "ai-interaction", "multi-step"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0"}, "devDependencies": {"@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/step1-mcp.git"}, "bugs": {"url": "https://github.com/yourusername/step1-mcp/issues"}, "homepage": "https://github.com/yourusername/step1-mcp#readme"}