#!/usr/bin/env node
/**
 * Step1 MCP Server
 * Main entry point for the Model Context Protocol server
 */
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
import { ProblemSolverTool } from './tools/problem-solver.js';
class Step1MCPServer {
    server;
    problemSolver;
    constructor() {
        this.server = new Server({
            name: 'step1-mcp',
            version: '1.0.0',
        });
        this.problemSolver = new ProblemSolverTool();
        this.setupHandlers();
    }
    setupHandlers() {
        // List available tools
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'problem_solver',
                        description: '5步问题解决助手：帮助用户通过结构化流程解决复杂问题并生成概念方案',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                action: {
                                    type: 'string',
                                    enum: ['start', 'confirm', 'adjust', 'answer', 'regenerate'],
                                    description: '操作类型：start(开始), confirm(确认), adjust(调整), answer(回答), regenerate(重新生成)'
                                },
                                content: {
                                    type: 'string',
                                    description: '操作内容：问题描述、调整说明、答案等'
                                },
                                sessionId: {
                                    type: 'string',
                                    description: '会话ID（start操作时可选，其他操作必需）'
                                }
                            },
                            required: ['action']
                        }
                    },
                    {
                        name: 'get_session_status',
                        description: '获取当前会话状态和进度',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                sessionId: {
                                    type: 'string',
                                    description: '会话ID'
                                }
                            },
                            required: ['sessionId']
                        }
                    },
                    {
                        name: 'reset_to_step',
                        description: '重置会话到指定步骤',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                sessionId: {
                                    type: 'string',
                                    description: '会话ID'
                                },
                                step: {
                                    type: 'number',
                                    minimum: 1,
                                    maximum: 5,
                                    description: '目标步骤号 (1-5)'
                                }
                            },
                            required: ['sessionId', 'step']
                        }
                    },
                    {
                        name: 'get_help',
                        description: '获取使用帮助和指南',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                            additionalProperties: false
                        }
                    },
                    {
                        name: 'get_stats',
                        description: '获取系统统计信息',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                            additionalProperties: false
                        }
                    }
                ]
            };
        });
        // Handle tool calls
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                switch (name) {
                    case 'problem_solver': {
                        const input = {
                            action: args?.['action'] || 'start',
                            content: args?.['content'] || '',
                            sessionId: args?.['sessionId']
                        };
                        const response = await this.problemSolver.execute(input);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: response.message
                                }
                            ],
                            isError: !response.success
                        };
                    }
                    case 'get_session_status': {
                        const response = await this.problemSolver.getSessionStatus(args?.['sessionId']);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: response.message
                                }
                            ],
                            isError: !response.success
                        };
                    }
                    case 'reset_to_step': {
                        const response = await this.problemSolver.resetToStep(args?.['sessionId'], args?.['step']);
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: response.message
                                }
                            ],
                            isError: !response.success
                        };
                    }
                    case 'get_help': {
                        const response = this.problemSolver.getHelp();
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: response.message
                                }
                            ]
                        };
                    }
                    case 'get_stats': {
                        const response = this.problemSolver.getStats();
                        return {
                            content: [
                                {
                                    type: 'text',
                                    text: response.message
                                }
                            ]
                        };
                    }
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                return {
                    content: [
                        {
                            type: 'text',
                            text: `错误：${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        });
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        // Log server start (to stderr so it doesn't interfere with MCP protocol)
        console.error('Step1 MCP Server started successfully');
    }
}
// Start the server
const server = new Step1MCPServer();
server.run().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map