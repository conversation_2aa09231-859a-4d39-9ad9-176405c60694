/**
 * Prompt Templates for Step1 MCP
 * Contains templates for AI interactions at each step
 */
import { PromptTemplate, StepNumber } from '../tools/types.js';
export declare class PromptTemplates {
    private templates;
    constructor();
    /**
     * Get template for specific step
     */
    getTemplate(step: StepNumber): PromptTemplate | undefined;
    /**
     * Format template with variables
     */
    formatTemplate(step: StepNumber, variables: Record<string, string>): string;
    /**
     * Initialize all prompt templates
     */
    private initializeTemplates;
    /**
     * Get step-specific guidance messages
     */
    getStepGuidance(step: StepNumber): string;
    /**
     * Get error messages for common scenarios
     */
    getErrorMessage(errorType: string): string;
    /**
     * Get confirmation prompts
     */
    getConfirmationPrompt(context: string): string;
    /**
     * Get progress indicator
     */
    getProgressIndicator(currentStep: StepNumber, totalSteps?: number): string;
}
//# sourceMappingURL=prompts.d.ts.map