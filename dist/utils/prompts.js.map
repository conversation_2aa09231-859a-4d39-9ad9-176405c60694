{"version": 3, "file": "prompts.js", "sourceRoot": "", "sources": ["../../src/utils/prompts.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,MAAM,OAAO,eAAe;IAClB,SAAS,GAAoC,IAAI,GAAG,EAAE,CAAC;IAE/D;QACE,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAAgB;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAgB,EAAE,SAAiC;QAChE,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,YAAY,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAClC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,wBAAwB;QACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YACpB,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE;;;;;;;;QAQR;YACF,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,oDAAoD;QACpD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YACpB,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE;;;;;;;;;;;;oBAYI;YACd,SAAS,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;SACpC,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YACpB,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE;;;;;;;;;;;;;4BAaY;YACtB,SAAS,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC;SAC1E,CAAC,CAAC;QAEH,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YACpB,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE;;;;;;;;;;;;;;;;;;QAkBR;YACF,SAAS,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC;SAC1G,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;YACpB,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAmCe;YACzB,SAAS,EAAE;gBACT,kBAAkB,EAAE,oBAAoB,EAAE,0BAA0B;gBACpE,kBAAkB,EAAE,oBAAoB,EAAE,0BAA0B;gBACpE,mBAAmB,EAAE,qBAAqB,EAAE,2BAA2B;aACxE;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAgB;QAC9B,MAAM,QAAQ,GAA+B;YAC3C,CAAC,EAAE,wBAAwB;YAC3B,CAAC,EAAE,2BAA2B;YAC9B,CAAC,EAAE,0BAA0B;YAC7B,CAAC,EAAE,uBAAuB;YAC1B,CAAC,EAAE,wBAAwB;SAC5B,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,SAAiB;QAC/B,MAAM,aAAa,GAA2B;YAC5C,aAAa,EAAE,kBAAkB;YACjC,gBAAgB,EAAE,mBAAmB;YACrC,mBAAmB,EAAE,cAAc;YACnC,cAAc,EAAE,iBAAiB;YACjC,cAAc,EAAE,kBAAkB;YAClC,SAAS,EAAE,cAAc;SAC1B,CAAC;QAEF,OAAO,aAAa,CAAC,SAAS,CAAC,IAAI,aAAa,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,OAAe;QACnC,OAAO,GAAG,OAAO,gFAAgF,CAAC;IACpG,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,WAAuB,EAAE,aAAqB,CAAC;QAClE,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC,CAAC;QAChF,OAAO,MAAM,QAAQ,KAAK,WAAW,IAAI,UAAU,GAAG,CAAC;IACzD,CAAC;CACF"}