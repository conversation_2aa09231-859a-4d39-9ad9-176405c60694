/**
 * Prompt Templates for Step1 MCP
 * Contains templates for AI interactions at each step
 */
export class PromptTemplates {
    templates = new Map();
    constructor() {
        this.initializeTemplates();
    }
    /**
     * Get template for specific step
     */
    getTemplate(step) {
        return this.templates.get(step);
    }
    /**
     * Format template with variables
     */
    formatTemplate(step, variables) {
        const template = this.getTemplate(step);
        if (!template) {
            throw new Error(`Template for step ${step} not found`);
        }
        let formatted = template.template;
        for (const [key, value] of Object.entries(variables)) {
            formatted = formatted.replace(new RegExp(`{{${key}}}`, 'g'), value);
        }
        return formatted;
    }
    /**
     * Initialize all prompt templates
     */
    initializeTemplates() {
        // Step 1: Problem Input
        this.templates.set(1, {
            step: 1,
            template: `欢迎使用问题解决助手！

请详细描述您需要解决的问题。为了更好地帮助您，请尽量包含以下信息：
- 问题的具体情况
- 您希望达到的目标
- 当前面临的主要挑战
- 任何相关的背景信息

您的问题描述：`,
            variables: []
        });
        // Step 2: Problem Restatement & Role Identification
        this.templates.set(2, {
            step: 2,
            template: `基于您的问题描述，我需要确保正确理解您的需求。

**问题重述：**
{{restatement}}

**建议的专业角色：**
{{roles}}

请确认：
1. 这个重述是否准确反映了您的问题？
2. 建议的专业角色是否合适？

如需调整，请告诉我具体需要修改的地方。`,
            variables: ['restatement', 'roles']
        });
        // Step 3: Research Plan Development
        this.templates.set(3, {
            step: 3,
            template: `根据确认的问题和角色，我制定了以下研究计划：

**计划标题：** {{title}}

**计划描述：** {{description}}

**执行步骤：**
{{steps}}

**预计总时间：** {{duration}}

**所需角色：** {{requiredRoles}}

这个计划是否符合您的期望？如需调整，请告诉我您的想法。`,
            variables: ['title', 'description', 'steps', 'duration', 'requiredRoles']
        });
        // Step 4: Plan Execution & Confirmation
        this.templates.set(4, {
            step: 4,
            template: `正在执行研究计划的第{{stepNumber}}步：

**当前步骤：** {{stepTitle}}
**负责角色：** {{stepRole}}
**步骤描述：** {{stepDescription}}

**执行结果：**
{{result}}

{{#if isKeyStep}}
这是一个关键步骤，请确认：
1. 结果是否符合您的期望？
2. 是否需要进一步澄清或调整？

{{#if questions}}
**需要您确认的问题：**
{{questions}}
{{/if}}
{{/if}}`,
            variables: ['stepNumber', 'stepTitle', 'stepRole', 'stepDescription', 'result', 'isKeyStep', 'questions']
        });
        // Step 5: Summary Generation
        this.templates.set(5, {
            step: 5,
            template: `恭喜！问题解决流程已完成。以下是三个不同角度的总结：

**1. 执行总结**
{{executiveSummary}}

**关键要点：**
{{executiveKeyPoints}}

**建议：**
{{executiveRecommendations}}

---

**2. 技术总结**
{{technicalSummary}}

**关键要点：**
{{technicalKeyPoints}}

**建议：**
{{technicalRecommendations}}

---

**3. 行动建议**
{{actionableSummary}}

**关键要点：**
{{actionableKeyPoints}}

**建议：**
{{actionableRecommendations}}

---

您对这些总结满意吗？如果不满意，我可以根据您的反馈重新生成。`,
            variables: [
                'executiveSummary', 'executiveKeyPoints', 'executiveRecommendations',
                'technicalSummary', 'technicalKeyPoints', 'technicalRecommendations',
                'actionableSummary', 'actionableKeyPoints', 'actionableRecommendations'
            ]
        });
    }
    /**
     * Get step-specific guidance messages
     */
    getStepGuidance(step) {
        const guidance = {
            1: "请详细描述您的问题，包含背景信息和期望目标。",
            2: "请确认问题重述和建议角色是否准确，如需调整请说明。",
            3: "请审查研究计划，确认步骤安排和时间估算是否合理。",
            4: "我将逐步执行计划，关键步骤会请您确认结果。",
            5: "请查看三个总结，确认是否满意或需要重新生成。"
        };
        return guidance[step] || "请按照提示进行操作。";
    }
    /**
     * Get error messages for common scenarios
     */
    getErrorMessage(errorType) {
        const errorMessages = {
            'EMPTY_INPUT': '输入不能为空，请提供有效的内容。',
            'INVALID_ACTION': '无效的操作，请选择正确的操作类型。',
            'SESSION_NOT_FOUND': '会话未找到，请重新开始。',
            'INVALID_STEP': '当前步骤无效，请检查操作流程。',
            'MISSING_DATA': '缺少必要的数据，请完成前续步骤。',
            'TIMEOUT': '会话已超时，请重新开始。'
        };
        return errorMessages[errorType] || '发生未知错误，请重试。';
    }
    /**
     * Get confirmation prompts
     */
    getConfirmationPrompt(context) {
        return `${context}\n\n请选择您的操作：\n- 输入 "confirm" 确认继续\n- 输入 "adjust" 并说明需要调整的内容\n- 输入 "back" 返回上一步`;
    }
    /**
     * Get progress indicator
     */
    getProgressIndicator(currentStep, totalSteps = 5) {
        const progress = '●'.repeat(currentStep) + '○'.repeat(totalSteps - currentStep);
        return `进度：${progress} (${currentStep}/${totalSteps})`;
    }
}
//# sourceMappingURL=prompts.js.map