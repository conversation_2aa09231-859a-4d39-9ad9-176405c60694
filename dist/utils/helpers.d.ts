/**
 * Helper utilities for Step1 MCP
 * Common utility functions used across the application
 */
import { StepNumber, SessionState, ExecutionResult } from '../tools/types.js';
/**
 * Format date for display
 */
export declare function formatDate(date: Date): string;
/**
 * Calculate session duration
 */
export declare function getSessionDuration(session: SessionState): string;
/**
 * Get step completion percentage
 */
export declare function getCompletionPercentage(currentStep: StepNumber): number;
/**
 * Format step progress as text
 */
export declare function formatStepProgress(currentStep: StepNumber, totalSteps?: number): string;
/**
 * Truncate text to specified length
 */
export declare function truncateText(text: string, maxLength?: number): string;
/**
 * Extract keywords from text
 */
export declare function extractKeywords(text: string, maxKeywords?: number): string[];
/**
 * Generate session summary
 */
export declare function generateSessionSummary(session: SessionState): string;
/**
 * Validate email format (if needed for notifications)
 */
export declare function isValidEmail(email: string): boolean;
/**
 * Generate random ID
 */
export declare function generateId(prefix?: string, length?: number): string;
/**
 * Deep clone object
 */
export declare function deepClone<T>(obj: T): T;
/**
 * Debounce function
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Retry function with exponential backoff
 */
export declare function retry<T>(fn: () => Promise<T>, maxAttempts?: number, baseDelay?: number): Promise<T>;
/**
 * Format file size
 */
export declare function formatFileSize(bytes: number): string;
/**
 * Check if string is JSON
 */
export declare function isJsonString(str: string): boolean;
/**
 * Safe JSON parse
 */
export declare function safeJsonParse<T>(str: string, defaultValue: T): T;
/**
 * Calculate execution progress
 */
export declare function calculateExecutionProgress(results: ExecutionResult[]): {
    completed: number;
    total: number;
    percentage: number;
};
/**
 * Format execution time estimate
 */
export declare function formatTimeEstimate(minutes: number): string;
/**
 * Get relative time description
 */
export declare function getRelativeTime(date: Date): string;
//# sourceMappingURL=helpers.d.ts.map