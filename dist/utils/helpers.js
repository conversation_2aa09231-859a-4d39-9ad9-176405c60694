/**
 * Helper utilities for Step1 MCP
 * Common utility functions used across the application
 */
/**
 * Format date for display
 */
export function formatDate(date) {
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}
/**
 * Calculate session duration
 */
export function getSessionDuration(session) {
    const durationMs = session.updatedAt.getTime() - session.createdAt.getTime();
    const minutes = Math.floor(durationMs / 60000);
    const seconds = Math.floor((durationMs % 60000) / 1000);
    if (minutes > 0) {
        return `${minutes}分${seconds}秒`;
    }
    return `${seconds}秒`;
}
/**
 * Get step completion percentage
 */
export function getCompletionPercentage(currentStep) {
    return Math.round((currentStep / 5) * 100);
}
/**
 * Format step progress as text
 */
export function formatStepProgress(currentStep, totalSteps = 5) {
    const completed = '✓'.repeat(currentStep - 1);
    const current = '●';
    const remaining = '○'.repeat(totalSteps - currentStep);
    return `${completed}${current}${remaining} (${currentStep}/${totalSteps})`;
}
/**
 * Truncate text to specified length
 */
export function truncateText(text, maxLength = 100) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength - 3) + '...';
}
/**
 * Extract keywords from text
 */
export function extractKeywords(text, maxKeywords = 5) {
    // Simple keyword extraction - in production, this could use NLP
    const words = text
        .toLowerCase()
        .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '') // Keep Chinese, English, numbers
        .split(/\s+/)
        .filter(word => word.length > 1);
    // Count word frequency
    const wordCount = new Map();
    words.forEach(word => {
        wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });
    // Sort by frequency and return top keywords
    return Array.from(wordCount.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, maxKeywords)
        .map(([word]) => word);
}
/**
 * Generate session summary
 */
export function generateSessionSummary(session) {
    const duration = getSessionDuration(session);
    const completion = getCompletionPercentage(session.currentStep);
    const keywords = extractKeywords(session.userProblem, 3);
    return `会话概要：
- 问题关键词：${keywords.join('、')}
- 当前进度：${completion}%
- 用时：${duration}
- 状态：${session.isComplete ? '已完成' : '进行中'}`;
}
/**
 * Validate email format (if needed for notifications)
 */
export function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
/**
 * Generate random ID
 */
export function generateId(prefix = '', length = 8) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return prefix ? `${prefix}_${result}` : result;
}
/**
 * Deep clone object
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    if (Array.isArray(obj)) {
        return obj.map(item => deepClone(item));
    }
    const cloned = {};
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}
/**
 * Debounce function
 */
export function debounce(func, wait) {
    let timeout = null;
    return (...args) => {
        if (timeout) {
            clearTimeout(timeout);
        }
        timeout = setTimeout(() => {
            func(...args);
        }, wait);
    };
}
/**
 * Retry function with exponential backoff
 */
export async function retry(fn, maxAttempts = 3, baseDelay = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error instanceof Error ? error : new Error(String(error));
            if (attempt === maxAttempts) {
                throw lastError;
            }
            // Exponential backoff
            const delay = baseDelay * Math.pow(2, attempt - 1);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
    throw lastError;
}
/**
 * Format file size
 */
export function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    return `${size.toFixed(1)} ${units[unitIndex]}`;
}
/**
 * Check if string is JSON
 */
export function isJsonString(str) {
    try {
        JSON.parse(str);
        return true;
    }
    catch {
        return false;
    }
}
/**
 * Safe JSON parse
 */
export function safeJsonParse(str, defaultValue) {
    try {
        return JSON.parse(str);
    }
    catch {
        return defaultValue;
    }
}
/**
 * Calculate execution progress
 */
export function calculateExecutionProgress(results) {
    const completed = results.filter(r => r.status === 'completed').length;
    const total = results.length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    return { completed, total, percentage };
}
/**
 * Format execution time estimate
 */
export function formatTimeEstimate(minutes) {
    if (minutes < 60) {
        return `约${minutes}分钟`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
        return `约${hours}小时`;
    }
    return `约${hours}小时${remainingMinutes}分钟`;
}
/**
 * Get relative time description
 */
export function getRelativeTime(date) {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    if (diffMinutes < 1) {
        return '刚刚';
    }
    else if (diffMinutes < 60) {
        return `${diffMinutes}分钟前`;
    }
    else if (diffMinutes < 1440) {
        const hours = Math.floor(diffMinutes / 60);
        return `${hours}小时前`;
    }
    else {
        const days = Math.floor(diffMinutes / 1440);
        return `${days}天前`;
    }
}
//# sourceMappingURL=helpers.js.map