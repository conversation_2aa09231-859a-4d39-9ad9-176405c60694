/**
 * Step Handlers for Step1 MCP
 * Implements the logic for each of the 5 interaction steps
 */
import { DEFAULT_CONFIG, Step1MCPError } from '../tools/types.js';
export class StepHandlers {
    stateManager;
    constructor(stateManager) {
        this.stateManager = stateManager;
    }
    /**
     * Step 1: Handle problem input
     */
    async handleStep1(input) {
        try {
            if (!input.content || input.content.trim().length === 0) {
                return {
                    success: false,
                    message: "请输入您需要解决的问题。问题描述应该清晰具体，这将帮助我更好地为您提供解决方案。",
                    sessionId: input.sessionId || ''
                };
            }
            // Create new session with the problem
            const session = this.stateManager.createSession(input.content);
            return {
                success: true,
                message: `问题已记录：${session.userProblem}\n\n现在我将进入第二步，对您的问题进行重述并识别需要的专业角色。`,
                data: {
                    problem: session.userProblem,
                    sessionId: session.sessionId
                },
                nextStep: 2,
                sessionId: session.sessionId
            };
        }
        catch (error) {
            return this.handleError(error, input.sessionId);
        }
    }
    /**
     * Step 2: Problem restatement and role identification
     */
    async handleStep2(input) {
        try {
            const session = this.getSessionOrThrow(input.sessionId);
            if (input.action === 'start' || !session.restatement) {
                // Generate problem restatement and roles
                const restatement = await this.generateProblemRestatement(session.userProblem);
                const roles = await this.identifyRequiredRoles(session.userProblem);
                this.stateManager.updateSession(session.sessionId, {
                    restatement,
                    roles: roles.map(r => r.name)
                });
                return {
                    success: true,
                    message: `**问题重述：**\n${restatement}\n\n**建议的专业角色：**\n${roles.map((r, i) => `${i + 1}. **${r.name}**: ${r.description}`).join('\n')}\n\n请确认这个重述是否准确反映了您想要解决的问题？如需调整，请告诉我。`,
                    data: {
                        restatement,
                        roles
                    },
                    requiresConfirmation: true,
                    sessionId: session.sessionId
                };
            }
            if (input.action === 'confirm') {
                // User confirmed, advance to step 3
                this.stateManager.advanceStep(session.sessionId);
                return {
                    success: true,
                    message: "很好！现在我将根据确认的问题和角色制定详细的研究计划。",
                    nextStep: 3,
                    sessionId: session.sessionId
                };
            }
            if (input.action === 'adjust') {
                // User wants to adjust
                const adjustedRestatement = await this.adjustProblemRestatement(session.userProblem, session.restatement, input.content);
                const adjustedRoles = await this.identifyRequiredRoles(adjustedRestatement);
                this.stateManager.updateSession(session.sessionId, {
                    restatement: adjustedRestatement,
                    roles: adjustedRoles.map(r => r.name)
                });
                return {
                    success: true,
                    message: `**调整后的问题重述：**\n${adjustedRestatement}\n\n**更新的专业角色：**\n${adjustedRoles.map((r, i) => `${i + 1}. **${r.name}**: ${r.description}`).join('\n')}\n\n请确认这个调整是否符合您的要求？`,
                    data: {
                        restatement: adjustedRestatement,
                        roles: adjustedRoles
                    },
                    requiresConfirmation: true,
                    sessionId: session.sessionId
                };
            }
            return {
                success: false,
                message: "请选择 'confirm'（确认）或 'adjust'（调整）操作。",
                sessionId: session.sessionId
            };
        }
        catch (error) {
            return this.handleError(error, input.sessionId);
        }
    }
    /**
     * Get session or throw error
     */
    getSessionOrThrow(sessionId) {
        const session = this.stateManager.getSession(sessionId);
        if (!session) {
            throw new Step1MCPError(`Session ${sessionId} not found`, 'SESSION_NOT_FOUND');
        }
        return session;
    }
    /**
     * Handle errors consistently
     */
    handleError(error, sessionId) {
        const message = error instanceof Error ? error.message : 'Unknown error occurred';
        return {
            success: false,
            message: `错误：${message}`,
            sessionId: sessionId || ''
        };
    }
    /**
     * Generate problem restatement using AI
     */
    async generateProblemRestatement(problem) {
        // This would typically call an AI service
        // For now, return a structured restatement
        return `基于您的描述，我理解您的核心问题是：${problem}。这个问题涉及多个方面的考虑，需要系统性的分析和解决方案设计。`;
    }
    /**
     * Identify required roles based on problem
     */
    async identifyRequiredRoles(problem) {
        // This would typically use AI to analyze the problem and suggest roles
        // For now, return default roles with some customization
        const defaultRoles = DEFAULT_CONFIG.defaultRoles;
        // Add problem-specific roles based on keywords
        const additionalRoles = [];
        if (problem.toLowerCase().includes('技术') || problem.toLowerCase().includes('系统')) {
            additionalRoles.push({
                name: 'Technical Expert',
                description: '技术专家，专注于技术解决方案',
                expertise: ['技术架构', '系统设计', '技术选型'],
                responsibilities: ['技术方案设计', '技术风险评估', '实施指导']
            });
        }
        return [...defaultRoles, ...additionalRoles].slice(0, 4); // 限制最多4个角色
    }
    /**
     * Step 3: Research plan development
     */
    async handleStep3(input) {
        try {
            const session = this.getSessionOrThrow(input.sessionId);
            if (input.action === 'start' || !session.researchPlan.steps.length) {
                // Generate research plan
                const researchPlan = await this.generateResearchPlan(session.restatement, session.roles);
                this.stateManager.updateSession(session.sessionId, { researchPlan });
                return {
                    success: true,
                    message: `**研究计划：${researchPlan.title}**\n\n${researchPlan.description}\n\n**执行步骤：**\n${researchPlan.steps.map((step, i) => `${i + 1}. **${step.title}** (${step.role})\n   ${step.description}\n   预计时间：${step.estimatedTime}${step.isKeyStep ? ' [需确认]' : ''}`).join('\n\n')}\n\n**总预计时间：** ${researchPlan.estimatedDuration}\n\n这个计划是否需要调整？请告诉我您的想法。`,
                    data: { researchPlan },
                    requiresConfirmation: true,
                    sessionId: session.sessionId
                };
            }
            if (input.action === 'confirm') {
                this.stateManager.advanceStep(session.sessionId);
                return {
                    success: true,
                    message: "计划已确认！现在开始按步骤执行研究计划。",
                    nextStep: 4,
                    sessionId: session.sessionId
                };
            }
            if (input.action === 'adjust') {
                const adjustedPlan = await this.adjustResearchPlan(session.researchPlan, input.content);
                this.stateManager.updateSession(session.sessionId, {
                    researchPlan: adjustedPlan
                });
                return {
                    success: true,
                    message: `**调整后的研究计划：**\n${adjustedPlan.description}\n\n请确认调整后的计划是否符合要求？`,
                    data: { researchPlan: adjustedPlan },
                    requiresConfirmation: true,
                    sessionId: session.sessionId
                };
            }
            return {
                success: false,
                message: "请选择 'confirm'（确认）或 'adjust'（调整）操作。",
                sessionId: session.sessionId
            };
        }
        catch (error) {
            return this.handleError(error, input.sessionId);
        }
    }
    /**
     * Step 4: Plan execution and confirmation
     */
    async handleStep4(input) {
        try {
            const session = this.getSessionOrThrow(input.sessionId);
            if (input.action === 'start') {
                // Start executing the first step
                const firstStep = session.researchPlan.steps[0];
                if (!firstStep) {
                    throw new Step1MCPError('No steps in research plan', 'INVALID_PLAN');
                }
                const result = await this.executeStep(firstStep, session);
                this.stateManager.updateSession(session.sessionId, {
                    executionResults: [result]
                });
                if (firstStep.isKeyStep) {
                    return {
                        success: true,
                        message: `**执行步骤：${firstStep.title}**\n\n${result.result}\n\n这是一个关键步骤，请确认结果是否满意？如有疑问，我可以为您解答。`,
                        data: { result },
                        requiresConfirmation: true,
                        questions: result.questions,
                        sessionId: session.sessionId
                    };
                }
                else {
                    // Continue to next step automatically
                    return this.continueExecution(session);
                }
            }
            if (input.action === 'confirm') {
                // Mark current step as confirmed and continue
                const currentResults = session.executionResults;
                if (currentResults.length > 0) {
                    const lastResult = currentResults[currentResults.length - 1];
                    if (lastResult) {
                        lastResult.userConfirmed = true;
                    }
                }
                this.stateManager.updateSession(session.sessionId, { executionResults: currentResults });
                return this.continueExecution(session);
            }
            if (input.action === 'answer') {
                // Handle user answers to questions
                const currentResults = session.executionResults;
                if (currentResults.length > 0) {
                    const lastResult = currentResults[currentResults.length - 1];
                    if (lastResult) {
                        lastResult.answers.push(input.content);
                    }
                }
                this.stateManager.updateSession(session.sessionId, { executionResults: currentResults });
                return {
                    success: true,
                    message: "答案已记录。还有其他问题需要澄清吗？如果没有，请确认继续下一步。",
                    sessionId: session.sessionId
                };
            }
            return {
                success: false,
                message: "请选择 'confirm'（确认）、'answer'（回答问题）操作。",
                sessionId: session.sessionId
            };
        }
        catch (error) {
            return this.handleError(error, input.sessionId);
        }
    }
    /**
     * Step 5: Summary generation
     */
    async handleStep5(input) {
        try {
            const session = this.getSessionOrThrow(input.sessionId);
            if (input.action === 'start' || session.summaries.length === 0) {
                // Generate 3 summaries
                const summaries = await this.generateSummaries(session);
                this.stateManager.updateSession(session.sessionId, { summaries });
                return {
                    success: true,
                    message: `**完成！以下是3个总结：**\n\n**1. 执行总结**\n${summaries[0]?.content || '生成失败'}\n\n**2. 技术总结**\n${summaries[1]?.content || '生成失败'}\n\n**3. 行动建议**\n${summaries[2]?.content || '生成失败'}\n\n您对这些总结满意吗？如不满意，我可以重新生成。`,
                    data: { summaries },
                    requiresConfirmation: true,
                    sessionId: session.sessionId
                };
            }
            if (input.action === 'confirm') {
                this.stateManager.completeSession(session.sessionId);
                return {
                    success: true,
                    message: "太好了！问题解决流程已完成。感谢您的参与，希望这些总结对您有帮助！",
                    data: { isComplete: true },
                    sessionId: session.sessionId
                };
            }
            if (input.action === 'regenerate') {
                const newSummaries = await this.generateSummaries(session, input.content);
                this.stateManager.updateSession(session.sessionId, { summaries: newSummaries });
                return {
                    success: true,
                    message: `**重新生成的总结：**\n\n**1. 执行总结**\n${newSummaries[0]?.content || '生成失败'}\n\n**2. 技术总结**\n${newSummaries[1]?.content || '生成失败'}\n\n**3. 行动建议**\n${newSummaries[2]?.content || '生成失败'}\n\n现在满意吗？`,
                    data: { summaries: newSummaries },
                    requiresConfirmation: true,
                    sessionId: session.sessionId
                };
            }
            return {
                success: false,
                message: "请选择 'confirm'（确认满意）或 'regenerate'（重新生成）操作。",
                sessionId: session.sessionId
            };
        }
        catch (error) {
            return this.handleError(error, input.sessionId);
        }
    }
    /**
     * Continue execution to next step
     */
    async continueExecution(session) {
        const completedSteps = session.executionResults.length;
        const totalSteps = session.researchPlan.steps.length;
        if (completedSteps >= totalSteps) {
            // All steps completed, advance to step 5
            this.stateManager.advanceStep(session.sessionId);
            return {
                success: true,
                message: "所有计划步骤已完成！现在进入最后阶段，生成总结报告。",
                nextStep: 5,
                sessionId: session.sessionId
            };
        }
        // Execute next step
        const nextStep = session.researchPlan.steps[completedSteps];
        if (!nextStep) {
            throw new Step1MCPError('No more steps to execute', 'INVALID_PLAN');
        }
        const result = await this.executeStep(nextStep, session);
        const updatedResults = [...session.executionResults, result];
        this.stateManager.updateSession(session.sessionId, {
            executionResults: updatedResults
        });
        if (nextStep.isKeyStep) {
            return {
                success: true,
                message: `**执行步骤：${nextStep.title}**\n\n${result.result}\n\n这是一个关键步骤，请确认结果是否满意？`,
                data: { result },
                requiresConfirmation: true,
                questions: result.questions,
                sessionId: session.sessionId
            };
        }
        else {
            // Continue automatically
            return this.continueExecution(session);
        }
    }
    /**
     * Generate research plan based on problem and roles
     */
    async generateResearchPlan(restatement, roles) {
        // This would typically use AI to generate a comprehensive plan
        const steps = [
            {
                id: 'step_1',
                title: '问题分析与需求收集',
                description: '深入分析问题的各个方面，收集详细需求',
                role: roles[0] || 'Problem Analyst',
                dependencies: [],
                estimatedTime: '30分钟',
                isKeyStep: true
            },
            {
                id: 'step_2',
                title: '解决方案设计',
                description: '基于分析结果设计可行的解决方案',
                role: roles[1] || 'Solution Architect',
                dependencies: ['step_1'],
                estimatedTime: '45分钟',
                isKeyStep: true
            },
            {
                id: 'step_3',
                title: '实施计划制定',
                description: '制定详细的实施计划和时间表',
                role: roles[2] || 'Implementation Specialist',
                dependencies: ['step_2'],
                estimatedTime: '30分钟',
                isKeyStep: false
            }
        ];
        return {
            title: '问题解决研究计划',
            description: `针对"${restatement}"的系统性解决方案研究计划`,
            steps,
            estimatedDuration: '约2小时',
            requiredRoles: roles
        };
    }
    /**
     * Adjust research plan based on user feedback
     */
    async adjustResearchPlan(currentPlan, feedback) {
        // This would typically use AI to adjust the plan
        return {
            ...currentPlan,
            description: `${currentPlan.description}（根据反馈调整：${feedback}）`,
            steps: currentPlan.steps.map(step => ({
                ...step,
                description: `${step.description}（已根据用户反馈优化）`
            }))
        };
    }
    /**
     * Execute a single step of the research plan
     */
    async executeStep(step, _session) {
        // This would typically use AI to execute the step
        const questions = [];
        if (step.isKeyStep) {
            questions.push(`关于${step.title}，您是否有特殊要求？`);
            questions.push(`在${step.description}过程中，有什么需要特别注意的吗？`);
        }
        return {
            stepId: step.id,
            status: 'completed',
            result: `已完成${step.title}：\n\n基于${step.role}的专业视角，我们分析了相关问题并得出了初步结论。具体包括：\n1. 关键发现和洞察\n2. 潜在解决方案\n3. 风险评估\n4. 下一步建议`,
            userConfirmed: false,
            questions: questions.slice(0, DEFAULT_CONFIG.maxQuestionsPerStep),
            answers: [],
            executedAt: new Date()
        };
    }
    /**
     * Generate 3 types of summaries
     */
    async generateSummaries(session, feedback) {
        // This would typically use AI to generate comprehensive summaries
        const summaries = [
            {
                type: 'executive',
                title: '执行总结',
                content: `针对"${session.restatement}"，我们通过${session.roles.join('、')}等专业角色的协作，完成了系统性的问题分析和解决方案设计。主要成果包括问题深度分析、可行性方案设计和实施路径规划。`,
                keyPoints: [
                    '问题核心已明确识别',
                    '解决方案具有可操作性',
                    '实施计划切实可行'
                ],
                recommendations: [
                    '建议按计划分阶段实施',
                    '定期评估进展和调整',
                    '保持各角色间的协调配合'
                ]
            },
            {
                type: 'technical',
                title: '技术总结',
                content: `从技术角度分析，该问题涉及多个技术层面的考虑。通过专业分析，我们识别了关键技术要点和实施难点，并提出了相应的技术解决方案。`,
                keyPoints: [
                    '技术架构清晰合理',
                    '实施方案技术可行',
                    '风险控制措施完善'
                ],
                recommendations: [
                    '优先解决核心技术问题',
                    '建立技术风险监控机制',
                    '确保技术方案的可扩展性'
                ]
            },
            {
                type: 'actionable',
                title: '行动建议',
                content: `基于完整的分析和规划，我们建议采取分步骤的行动方案。首先解决最关键的问题，然后逐步推进其他方面的改进。`,
                keyPoints: [
                    '行动计划具体明确',
                    '优先级排序合理',
                    '资源配置优化'
                ],
                recommendations: [
                    '立即启动第一阶段行动',
                    '建立进度跟踪机制',
                    '准备应急预案'
                ]
            }
        ];
        if (feedback) {
            // Adjust summaries based on feedback
            summaries.forEach(summary => {
                summary.content = `${summary.content}\n\n（根据您的反馈"${feedback}"进行了调整）`;
            });
        }
        return summaries;
    }
    /**
     * Adjust problem restatement based on user feedback
     */
    async adjustProblemRestatement(originalProblem, _currentRestatement, userFeedback) {
        // This would typically use AI to adjust the restatement
        return `根据您的反馈调整：${originalProblem}。考虑到您提到的${userFeedback}，我重新理解您的问题重点在于...`;
    }
}
//# sourceMappingURL=step-handlers.js.map