/**
 * Input Validators for Step1 MCP
 * Validates user inputs and ensures data integrity
 */
import { DEFAULT_CONFIG } from '../tools/types.js';
export class InputValidator {
    /**
     * Validate user input for any step
     */
    validateInput(input, currentStep) {
        const errors = [];
        const warnings = [];
        // Basic validation
        if (!input) {
            errors.push('输入不能为空');
            return { isValid: false, errors, warnings };
        }
        // Validate action
        if (!this.isValidAction(input.action)) {
            errors.push(`无效的操作类型: ${input.action}`);
        }
        // Validate content based on action
        if (input.action !== 'start' && (!input.content || input.content.trim().length === 0)) {
            errors.push('操作内容不能为空');
        }
        // Step-specific validation
        if (currentStep) {
            const stepValidation = this.validateStepSpecific(input, currentStep);
            errors.push(...stepValidation.errors);
            warnings.push(...stepValidation.warnings);
        }
        // Content length validation
        if (input.content && input.content.length > 5000) {
            warnings.push('输入内容较长，建议简化描述');
        }
        // Session ID validation for non-start actions
        if (input.action !== 'start' && !input.sessionId) {
            errors.push('缺少会话ID');
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
     * Validate problem description (Step 1)
     */
    validateProblemDescription(description) {
        const errors = [];
        const warnings = [];
        if (!description || description.trim().length === 0) {
            errors.push('问题描述不能为空');
            return { isValid: false, errors, warnings };
        }
        if (description.length < 10) {
            warnings.push('问题描述过于简短，建议提供更多细节');
        }
        if (description.length > 2000) {
            warnings.push('问题描述过长，建议精简表达');
        }
        // Check for common issues
        if (!this.containsQuestionWords(description)) {
            warnings.push('建议明确说明具体问题或挑战');
        }
        if (!this.containsGoalWords(description)) {
            warnings.push('建议说明期望达到的目标');
        }
        return { isValid: true, errors, warnings };
    }
    /**
     * Validate confirmation input (Steps 2, 3, 5)
     */
    validateConfirmation(input, allowedActions) {
        const errors = [];
        const warnings = [];
        if (!input || input.trim().length === 0) {
            errors.push('请选择操作：confirm（确认）或 adjust（调整）');
            return { isValid: false, errors, warnings };
        }
        const action = input.toLowerCase().trim();
        if (!allowedActions.includes(action)) {
            errors.push(`无效操作，请选择：${allowedActions.join('、')}`);
        }
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
     * Validate adjustment feedback
     */
    validateAdjustmentFeedback(feedback) {
        const errors = [];
        const warnings = [];
        if (!feedback || feedback.trim().length === 0) {
            errors.push('请说明需要调整的具体内容');
            return { isValid: false, errors, warnings };
        }
        if (feedback.length < 5) {
            warnings.push('调整说明过于简短，建议提供更多细节');
        }
        return { isValid: true, errors, warnings };
    }
    /**
     * Validate answer to questions (Step 4)
     */
    validateAnswer(answer, questionCount) {
        const errors = [];
        const warnings = [];
        if (!answer || answer.trim().length === 0) {
            errors.push('回答不能为空');
            return { isValid: false, errors, warnings };
        }
        if (answer.length < 3) {
            warnings.push('回答过于简短，建议提供更多信息');
        }
        if (questionCount > DEFAULT_CONFIG.maxQuestionsPerStep) {
            warnings.push(`问题数量较多(${questionCount})，建议重点回答关键问题`);
        }
        return { isValid: true, errors, warnings };
    }
    /**
     * Validate session ID format
     */
    validateSessionId(sessionId) {
        if (!sessionId)
            return false;
        // Expected format: session_timestamp_randomstring
        const pattern = /^session_\d+_[a-z0-9]+$/;
        return pattern.test(sessionId);
    }
    /**
     * Check if action is valid
     */
    isValidAction(action) {
        const validActions = ['start', 'confirm', 'adjust', 'answer', 'regenerate', 'back'];
        return validActions.includes(action);
    }
    /**
     * Step-specific validation
     */
    validateStepSpecific(input, step) {
        const errors = [];
        const warnings = [];
        switch (step) {
            case 1:
                if (input.action === 'start') {
                    const problemValidation = this.validateProblemDescription(input.content);
                    errors.push(...problemValidation.errors);
                    warnings.push(...problemValidation.warnings);
                }
                break;
            case 2:
                if (input.action === 'confirm' || input.action === 'adjust') {
                    if (input.action === 'adjust' && input.content) {
                        const adjustValidation = this.validateAdjustmentFeedback(input.content);
                        errors.push(...adjustValidation.errors);
                        warnings.push(...adjustValidation.warnings);
                    }
                }
                else {
                    errors.push('步骤2只支持 confirm 或 adjust 操作');
                }
                break;
            case 3:
                if (input.action === 'confirm' || input.action === 'adjust') {
                    if (input.action === 'adjust' && input.content) {
                        const adjustValidation = this.validateAdjustmentFeedback(input.content);
                        errors.push(...adjustValidation.errors);
                        warnings.push(...adjustValidation.warnings);
                    }
                }
                else {
                    errors.push('步骤3只支持 confirm 或 adjust 操作');
                }
                break;
            case 4:
                if (!['start', 'confirm', 'answer'].includes(input.action)) {
                    errors.push('步骤4只支持 start、confirm 或 answer 操作');
                }
                if (input.action === 'answer' && input.content) {
                    const answerValidation = this.validateAnswer(input.content, 1);
                    errors.push(...answerValidation.errors);
                    warnings.push(...answerValidation.warnings);
                }
                break;
            case 5:
                if (!['start', 'confirm', 'regenerate'].includes(input.action)) {
                    errors.push('步骤5只支持 start、confirm 或 regenerate 操作');
                }
                break;
        }
        return { isValid: errors.length === 0, errors, warnings };
    }
    /**
     * Check if description contains question-related words
     */
    containsQuestionWords(text) {
        const questionWords = ['问题', '困难', '挑战', '如何', '怎么', '为什么', '什么', '哪里', '疑问', '难题'];
        return questionWords.some(word => text.includes(word));
    }
    /**
     * Check if description contains goal-related words
     */
    containsGoalWords(text) {
        const goalWords = ['目标', '希望', '想要', '期望', '需要', '实现', '达到', '完成', '解决', '改善'];
        return goalWords.some(word => text.includes(word));
    }
    /**
     * Sanitize input content
     */
    sanitizeInput(input) {
        if (!input)
            return '';
        return input
            .trim()
            .replace(/\s+/g, ' ') // Replace multiple spaces with single space
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .substring(0, 5000); // Limit length
    }
    /**
     * Get validation summary message
     */
    getValidationSummary(result) {
        let message = '';
        if (result.errors.length > 0) {
            message += `错误：${result.errors.join('；')}\n`;
        }
        if (result.warnings.length > 0) {
            message += `提醒：${result.warnings.join('；')}`;
        }
        return message.trim();
    }
}
//# sourceMappingURL=validators.js.map