/**
 * State Manager for Step1 MCP
 * Manages session state and step transitions
 */
import { Step1MCPError, DEFAULT_CONFIG } from '../tools/types.js';
export class StateManager {
    sessions = new Map();
    config = DEFAULT_CONFIG;
    /**
     * Create a new session
     */
    createSession(userProblem) {
        const sessionId = this.generateSessionId();
        const now = new Date();
        const session = {
            currentStep: 1,
            userProblem: userProblem.trim(),
            restatement: '',
            roles: [],
            researchPlan: {
                title: '',
                description: '',
                steps: [],
                estimatedDuration: '',
                requiredRoles: []
            },
            executionResults: [],
            summaries: [],
            isComplete: false,
            sessionId,
            createdAt: now,
            updatedAt: now
        };
        this.sessions.set(sessionId, session);
        this.scheduleCleanup(sessionId);
        return session;
    }
    /**
     * Get session by ID
     */
    getSession(sessionId) {
        return this.sessions.get(sessionId) || null;
    }
    /**
     * Update session state
     */
    updateSession(sessionId, updates) {
        const session = this.getSession(sessionId);
        if (!session) {
            throw new Step1MCPError(`Session ${sessionId} not found`, 'SESSION_NOT_FOUND');
        }
        const updatedSession = {
            ...session,
            ...updates,
            updatedAt: new Date()
        };
        this.sessions.set(sessionId, updatedSession);
        return updatedSession;
    }
    /**
     * Advance to next step
     */
    advanceStep(sessionId) {
        const session = this.getSession(sessionId);
        if (!session) {
            throw new Step1MCPError(`Session ${sessionId} not found`, 'SESSION_NOT_FOUND');
        }
        if (session.currentStep >= 5) {
            throw new Step1MCPError('Already at final step', 'INVALID_STEP_TRANSITION');
        }
        const nextStep = (session.currentStep + 1);
        return this.updateSession(sessionId, { currentStep: nextStep });
    }
    /**
     * Go back to previous step
     */
    goBackStep(sessionId) {
        const session = this.getSession(sessionId);
        if (!session) {
            throw new Step1MCPError(`Session ${sessionId} not found`, 'SESSION_NOT_FOUND');
        }
        if (session.currentStep <= 1) {
            throw new Step1MCPError('Already at first step', 'INVALID_STEP_TRANSITION');
        }
        const prevStep = (session.currentStep - 1);
        return this.updateSession(sessionId, { currentStep: prevStep });
    }
    /**
     * Mark session as complete
     */
    completeSession(sessionId) {
        return this.updateSession(sessionId, {
            isComplete: true,
            currentStep: 5
        });
    }
    /**
     * Delete session
     */
    deleteSession(sessionId) {
        return this.sessions.delete(sessionId);
    }
    /**
     * Get all active sessions
     */
    getActiveSessions() {
        return Array.from(this.sessions.values()).filter(s => !s.isComplete);
    }
    /**
     * Validate step transition
     */
    canAdvanceToStep(sessionId, _targetStep) {
        const session = this.getSession(sessionId);
        if (!session)
            return false;
        // Check if required data for current step is complete
        switch (session.currentStep) {
            case 1:
                return session.userProblem.length > 0;
            case 2:
                return session.restatement.length > 0 && session.roles.length > 0;
            case 3:
                return session.researchPlan.steps.length > 0;
            case 4:
                return session.executionResults.length > 0;
            case 5:
                return session.summaries.length >= 3;
            default:
                return false;
        }
    }
    /**
     * Generate unique session ID
     */
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Schedule session cleanup after timeout
     */
    scheduleCleanup(sessionId) {
        const timeoutMs = this.config.sessionTimeoutMinutes * 60 * 1000;
        setTimeout(() => {
            const session = this.getSession(sessionId);
            if (session && !session.isComplete) {
                this.deleteSession(sessionId);
                console.log(`Session ${sessionId} cleaned up due to timeout`);
            }
        }, timeoutMs);
    }
    /**
     * Get session statistics
     */
    getStats() {
        const allSessions = Array.from(this.sessions.values());
        return {
            totalSessions: allSessions.length,
            activeSessions: allSessions.filter(s => !s.isComplete).length,
            completedSessions: allSessions.filter(s => s.isComplete).length
        };
    }
}
//# sourceMappingURL=state-manager.js.map