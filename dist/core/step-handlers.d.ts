/**
 * Step Handlers for Step1 MCP
 * Implements the logic for each of the 5 interaction steps
 */
import { UserInput, ToolResponse } from '../tools/types.js';
import { StateManager } from './state-manager.js';
export declare class StepHandlers {
    private stateManager;
    constructor(stateManager: StateManager);
    /**
     * Step 1: Handle problem input
     */
    handleStep1(input: UserInput): Promise<ToolResponse>;
    /**
     * Step 2: Problem restatement and role identification
     */
    handleStep2(input: UserInput): Promise<ToolResponse>;
    /**
     * Get session or throw error
     */
    private getSessionOrThrow;
    /**
     * Handle errors consistently
     */
    private handleError;
    /**
     * Generate problem restatement using AI
     */
    private generateProblemRestatement;
    /**
     * Identify required roles based on problem
     */
    private identifyRequiredRoles;
    /**
     * Step 3: Research plan development
     */
    handleStep3(input: UserInput): Promise<ToolResponse>;
    /**
     * Step 4: Plan execution and confirmation
     */
    handleStep4(input: UserInput): Promise<ToolResponse>;
    /**
     * Step 5: Summary generation
     */
    handleStep5(input: UserInput): Promise<ToolResponse>;
    /**
     * Continue execution to next step
     */
    private continueExecution;
    /**
     * Generate research plan based on problem and roles
     */
    private generateResearchPlan;
    /**
     * Adjust research plan based on user feedback
     */
    private adjustResearchPlan;
    /**
     * Execute a single step of the research plan
     */
    private executeStep;
    /**
     * Generate 3 types of summaries
     */
    private generateSummaries;
    /**
     * Adjust problem restatement based on user feedback
     */
    private adjustProblemRestatement;
}
//# sourceMappingURL=step-handlers.d.ts.map