/**
 * State Manager for Step1 MCP
 * Manages session state and step transitions
 */
import { SessionState, StepNumber } from '../tools/types.js';
export declare class StateManager {
    private sessions;
    private readonly config;
    /**
     * Create a new session
     */
    createSession(userProblem: string): SessionState;
    /**
     * Get session by ID
     */
    getSession(sessionId: string): SessionState | null;
    /**
     * Update session state
     */
    updateSession(sessionId: string, updates: Partial<SessionState>): SessionState;
    /**
     * Advance to next step
     */
    advanceStep(sessionId: string): SessionState;
    /**
     * Go back to previous step
     */
    goBackStep(sessionId: string): SessionState;
    /**
     * Mark session as complete
     */
    completeSession(sessionId: string): SessionState;
    /**
     * Delete session
     */
    deleteSession(sessionId: string): boolean;
    /**
     * Get all active sessions
     */
    getActiveSessions(): SessionState[];
    /**
     * Validate step transition
     */
    canAdvanceToStep(sessionId: string, _targetStep: StepNumber): boolean;
    /**
     * Generate unique session ID
     */
    private generateSessionId;
    /**
     * Schedule session cleanup after timeout
     */
    private scheduleCleanup;
    /**
     * Get session statistics
     */
    getStats(): {
        totalSessions: number;
        activeSessions: number;
        completedSessions: number;
    };
}
//# sourceMappingURL=state-manager.d.ts.map