/**
 * Input Validators for Step1 MCP
 * Validates user inputs and ensures data integrity
 */
import { UserInput, ValidationResult, StepNumber } from '../tools/types.js';
export declare class InputValidator {
    /**
     * Validate user input for any step
     */
    validateInput(input: UserInput, currentStep?: StepNumber): ValidationResult;
    /**
     * Validate problem description (Step 1)
     */
    validateProblemDescription(description: string): ValidationResult;
    /**
     * Validate confirmation input (Steps 2, 3, 5)
     */
    validateConfirmation(input: string, allowedActions: string[]): ValidationResult;
    /**
     * Validate adjustment feedback
     */
    validateAdjustmentFeedback(feedback: string): ValidationResult;
    /**
     * Validate answer to questions (Step 4)
     */
    validateAnswer(answer: string, questionCount: number): ValidationResult;
    /**
     * Validate session ID format
     */
    validateSessionId(sessionId: string): boolean;
    /**
     * Check if action is valid
     */
    private isValidAction;
    /**
     * Step-specific validation
     */
    private validateStepSpecific;
    /**
     * Check if description contains question-related words
     */
    private containsQuestionWords;
    /**
     * Check if description contains goal-related words
     */
    private containsGoalWords;
    /**
     * Sanitize input content
     */
    sanitizeInput(input: string): string;
    /**
     * Get validation summary message
     */
    getValidationSummary(result: ValidationResult): string;
}
//# sourceMappingURL=validators.d.ts.map