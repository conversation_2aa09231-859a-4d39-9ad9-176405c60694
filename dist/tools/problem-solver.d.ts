/**
 * Problem Solver MCP Tool
 * Main tool that orchestrates the 5-step problem solving process
 */
import { UserInput, ToolResponse, StepNumber } from './types.js';
export declare class ProblemSolverTool {
    private stateManager;
    private stepHandlers;
    private validator;
    private promptTemplates;
    constructor();
    /**
     * Main entry point for the problem solver tool
     */
    execute(input: UserInput): Promise<ToolResponse>;
    /**
     * Handle new session creation
     */
    private handleNewSession;
    /**
     * Route input to appropriate step handler
     */
    private routeToStepHandler;
    /**
     * Get current session status
     */
    getSessionStatus(sessionId: string): Promise<ToolResponse>;
    /**
     * Reset session to specific step
     */
    resetToStep(sessionId: string, targetStep: StepNumber): Promise<ToolResponse>;
    /**
     * Get help information
     */
    getHelp(): ToolResponse;
    /**
     * Get statistics
     */
    getStats(): ToolResponse;
    /**
     * Handle errors consistently
     */
    private handleError;
}
//# sourceMappingURL=problem-solver.d.ts.map