/**
 * Problem Solver MCP Tool
 * Main tool that orchestrates the 5-step problem solving process
 */
import { STEP_NAMES, Step1MCPError } from './types.js';
import { StateManager } from '../core/state-manager.js';
import { StepHandlers } from '../core/step-handlers.js';
import { InputValidator } from '../core/validators.js';
import { PromptTemplates } from '../utils/prompts.js';
export class ProblemSolverTool {
    stateManager;
    stepHandlers;
    validator;
    promptTemplates;
    constructor() {
        this.stateManager = new StateManager();
        this.promptTemplates = new PromptTemplates();
        this.stepHandlers = new StepHandlers(this.stateManager);
        this.validator = new InputValidator();
    }
    /**
     * Main entry point for the problem solver tool
     */
    async execute(input) {
        try {
            // Sanitize input
            if (input.content) {
                input.content = this.validator.sanitizeInput(input.content);
            }
            // Handle new session start
            if (input.action === 'start' && !input.sessionId) {
                return this.handleNewSession(input);
            }
            // Get existing session
            const session = this.stateManager.getSession(input.sessionId);
            if (!session) {
                return {
                    success: false,
                    message: "会话未找到，请重新开始。使用 action: 'start' 开始新的问题解决流程。",
                    sessionId: input.sessionId || ''
                };
            }
            // Validate input for current step
            const validation = this.validator.validateInput(input, session.currentStep);
            if (!validation.isValid) {
                return {
                    success: false,
                    message: this.validator.getValidationSummary(validation),
                    sessionId: session.sessionId
                };
            }
            // Route to appropriate step handler
            return this.routeToStepHandler(input, session.currentStep);
        }
        catch (error) {
            return this.handleError(error, input.sessionId);
        }
    }
    /**
     * Handle new session creation
     */
    async handleNewSession(input) {
        // Validate problem description
        const validation = this.validator.validateProblemDescription(input.content);
        if (!validation.isValid) {
            return {
                success: false,
                message: `请提供有效的问题描述：\n${this.validator.getValidationSummary(validation)}`,
                sessionId: ''
            };
        }
        // Show warnings if any
        let message = "欢迎使用问题解决助手！\n\n";
        if (validation.warnings.length > 0) {
            message += `提醒：${validation.warnings.join('；')}\n\n`;
        }
        // Create session and start step 1
        const response = await this.stepHandlers.handleStep1(input);
        if (response.success) {
            message += response.message;
            message += `\n\n${this.promptTemplates.getProgressIndicator(1)}`;
            return {
                ...response,
                message
            };
        }
        return response;
    }
    /**
     * Route input to appropriate step handler
     */
    async routeToStepHandler(input, currentStep) {
        let response;
        switch (currentStep) {
            case 1:
                response = await this.stepHandlers.handleStep1(input);
                break;
            case 2:
                response = await this.stepHandlers.handleStep2(input);
                break;
            case 3:
                response = await this.stepHandlers.handleStep3(input);
                break;
            case 4:
                response = await this.stepHandlers.handleStep4(input);
                break;
            case 5:
                response = await this.stepHandlers.handleStep5(input);
                break;
            default:
                throw new Step1MCPError(`Invalid step: ${currentStep}`, 'INVALID_STEP');
        }
        // Add progress indicator and step info
        if (response.success && !response.data?.isComplete) {
            const stepName = STEP_NAMES[currentStep];
            const nextStep = response.nextStep || currentStep;
            response.message = `**${stepName}**\n\n${response.message}`;
            if (nextStep !== currentStep) {
                response.message += `\n\n${this.promptTemplates.getProgressIndicator(nextStep)}`;
            }
        }
        return response;
    }
    /**
     * Get current session status
     */
    async getSessionStatus(sessionId) {
        try {
            const session = this.stateManager.getSession(sessionId);
            if (!session) {
                return {
                    success: false,
                    message: "会话未找到",
                    sessionId
                };
            }
            const stepName = STEP_NAMES[session.currentStep];
            const progress = this.promptTemplates.getProgressIndicator(session.currentStep);
            return {
                success: true,
                message: `当前状态：${stepName}\n${progress}\n\n${this.promptTemplates.getStepGuidance(session.currentStep)}`,
                data: {
                    currentStep: session.currentStep,
                    stepName,
                    isComplete: session.isComplete,
                    problem: session.userProblem
                },
                sessionId
            };
        }
        catch (error) {
            return this.handleError(error, sessionId);
        }
    }
    /**
     * Reset session to specific step
     */
    async resetToStep(sessionId, targetStep) {
        try {
            const session = this.stateManager.getSession(sessionId);
            if (!session) {
                return {
                    success: false,
                    message: "会话未找到",
                    sessionId
                };
            }
            if (targetStep < 1 || targetStep > 5) {
                return {
                    success: false,
                    message: "无效的步骤号，请选择1-5之间的数字",
                    sessionId
                };
            }
            // Reset session to target step
            this.stateManager.updateSession(sessionId, {
                currentStep: targetStep,
                isComplete: false
            });
            const stepName = STEP_NAMES[targetStep];
            const progress = this.promptTemplates.getProgressIndicator(targetStep);
            return {
                success: true,
                message: `已重置到${stepName}\n${progress}\n\n${this.promptTemplates.getStepGuidance(targetStep)}`,
                data: { currentStep: targetStep },
                sessionId
            };
        }
        catch (error) {
            return this.handleError(error, sessionId);
        }
    }
    /**
     * Get help information
     */
    getHelp() {
        return {
            success: true,
            message: `**问题解决助手使用指南**

**开始新会话：**
\`\`\`json
{
  "action": "start",
  "content": "您的问题描述"
}
\`\`\`

**5步解决流程：**
1. **问题输入** - 详细描述您的问题
2. **问题重述与角色识别** - 确认问题理解和专业角色
3. **制定研究计划** - 审查和调整解决方案计划
4. **计划执行与确认** - 分步执行并确认关键结果
5. **总结生成** - 获得三个角度的综合总结

**常用操作：**
- \`confirm\` - 确认当前步骤结果
- \`adjust\` - 调整当前步骤内容
- \`answer\` - 回答问题（步骤4）
- \`regenerate\` - 重新生成（步骤5）

**获取状态：** 使用 \`getSessionStatus\` 查看当前进度
**重置步骤：** 使用 \`resetToStep\` 返回特定步骤`,
            sessionId: ''
        };
    }
    /**
     * Get statistics
     */
    getStats() {
        const stats = this.stateManager.getStats();
        return {
            success: true,
            message: `**系统统计信息**

- 总会话数：${stats.totalSessions}
- 活跃会话：${stats.activeSessions}
- 已完成会话：${stats.completedSessions}`,
            data: stats,
            sessionId: ''
        };
    }
    /**
     * Handle errors consistently
     */
    handleError(error, sessionId) {
        let message = '发生未知错误';
        let code = 'UNKNOWN_ERROR';
        if (error instanceof Step1MCPError) {
            message = error.message;
            code = error.code;
        }
        else if (error instanceof Error) {
            message = error.message;
        }
        return {
            success: false,
            message: `错误 (${code}): ${message}`,
            sessionId: sessionId || ''
        };
    }
}
//# sourceMappingURL=problem-solver.js.map