/**
 * Type definitions for the Step1 MCP problem-solving tool
 */
// 错误类型
export class Step1MCPError extends Error {
    code;
    step;
    constructor(message, code, step) {
        super(message);
        this.code = code;
        this.step = step;
        this.name = 'Step1MCPError';
    }
}
// 常量
export const DEFAULT_CONFIG = {
    maxQuestionsPerStep: 3,
    sessionTimeoutMinutes: 60,
    enableLogging: true,
    defaultRoles: [
        {
            name: 'Problem Analyst',
            description: 'Analyzes and breaks down complex problems',
            expertise: ['Problem decomposition', 'Root cause analysis', 'Systems thinking'],
            responsibilities: ['Problem clarification', 'Requirement gathering', 'Scope definition']
        },
        {
            name: 'Solution Architect',
            description: 'Designs comprehensive solutions',
            expertise: ['Solution design', 'Architecture planning', 'Technology selection'],
            responsibilities: ['Solution blueprint', 'Technical planning', 'Risk assessment']
        },
        {
            name: 'Implementation Specialist',
            description: 'Focuses on practical implementation',
            expertise: ['Project management', 'Implementation planning', 'Resource allocation'],
            responsibilities: ['Execution planning', 'Timeline management', 'Quality assurance']
        }
    ]
};
export const STEP_NAMES = {
    1: 'Problem Input',
    2: 'Problem Restatement & Role Identification',
    3: 'Research Plan Development',
    4: 'Plan Execution & Confirmation',
    5: 'Summary Generation'
};
//# sourceMappingURL=types.js.map