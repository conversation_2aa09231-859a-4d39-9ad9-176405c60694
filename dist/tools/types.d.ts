/**
 * Type definitions for the Step1 MCP problem-solving tool
 */
export type StepNumber = 1 | 2 | 3 | 4 | 5;
export interface SessionState {
    currentStep: StepNumber;
    userProblem: string;
    restatement: string;
    roles: string[];
    researchPlan: ResearchPlan;
    executionResults: ExecutionResult[];
    summaries: Summary[];
    isComplete: boolean;
    sessionId: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface ResearchPlan {
    title: string;
    description: string;
    steps: PlanStep[];
    estimatedDuration: string;
    requiredRoles: string[];
}
export interface PlanStep {
    id: string;
    title: string;
    description: string;
    role: string;
    dependencies: string[];
    estimatedTime: string;
    isKeyStep: boolean;
}
export interface ExecutionResult {
    stepId: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed';
    result: string;
    userConfirmed: boolean;
    questions: string[];
    answers: string[];
    executedAt: Date;
}
export interface Summary {
    type: 'executive' | 'technical' | 'actionable';
    title: string;
    content: string;
    keyPoints: string[];
    recommendations: string[];
}
export interface UserInput {
    action: 'start' | 'confirm' | 'adjust' | 'answer' | 'regenerate';
    content: string;
    stepNumber?: StepNumber;
    sessionId?: string;
}
export interface ToolResponse {
    success: boolean;
    message: string;
    data?: any;
    nextStep?: StepNumber;
    requiresConfirmation?: boolean;
    questions?: string[];
    sessionId: string;
}
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}
export interface Role {
    name: string;
    description: string;
    expertise: string[];
    responsibilities: string[];
}
export interface PromptTemplate {
    step: StepNumber;
    template: string;
    variables: string[];
}
export interface Config {
    maxQuestionsPerStep: number;
    sessionTimeoutMinutes: number;
    enableLogging: boolean;
    defaultRoles: Role[];
}
export declare class Step1MCPError extends Error {
    code: string;
    step?: StepNumber | undefined;
    constructor(message: string, code: string, step?: StepNumber | undefined);
}
export declare const DEFAULT_CONFIG: Config;
export declare const STEP_NAMES: Record<StepNumber, string>;
//# sourceMappingURL=types.d.ts.map