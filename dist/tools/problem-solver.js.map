{"version": 3, "file": "problem-solver.js", "sourceRoot": "", "sources": ["../../src/tools/problem-solver.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAIL,UAAU,EACV,aAAa,EACd,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAEtD,MAAM,OAAO,iBAAiB;IACpB,YAAY,CAAe;IAC3B,YAAY,CAAe;IAC3B,SAAS,CAAiB;IAC1B,eAAe,CAAkB;IAEzC;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,KAAgB;QAC5B,IAAI,CAAC;YACH,iBAAiB;YACjB,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClB,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC;YAED,2BAA2B;YAC3B,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACjD,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;YAED,uBAAuB;YACvB,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,SAAU,CAAC,CAAC;YAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,4CAA4C;oBACrD,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,EAAE;iBACjC,CAAC;YACJ,CAAC;YAED,kCAAkC;YAClC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;YAC5E,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACxD,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B,CAAC;YACJ,CAAC;YAED,oCAAoC;YACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QAE7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,KAAgB;QAC7C,+BAA+B;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5E,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gBAAgB,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,UAAU,CAAC,EAAE;gBAC1E,SAAS,EAAE,EAAE;aACd,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,GAAG,iBAAiB,CAAC;QAChC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,MAAM,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QACvD,CAAC;QAED,kCAAkC;QAClC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAE5D,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC;YAC5B,OAAO,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC;YAEjE,OAAO;gBACL,GAAG,QAAQ;gBACX,OAAO;aACR,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAgB,EAAE,WAAuB;QACxE,IAAI,QAAsB,CAAC;QAE3B,QAAQ,WAAW,EAAE,CAAC;YACpB,KAAK,CAAC;gBACJ,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,CAAC;gBACJ,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,CAAC;gBACJ,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,CAAC;gBACJ,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,CAAC;gBACJ,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACtD,MAAM;YACR;gBACE,MAAM,IAAI,aAAa,CAAC,iBAAiB,WAAW,EAAE,EAAE,cAAc,CAAC,CAAC;QAC5E,CAAC;QAED,uCAAuC;QACvC,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,WAAW,CAAC;YAElD,QAAQ,CAAC,OAAO,GAAG,KAAK,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC;YAE5D,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;gBAC7B,QAAQ,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnF,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,OAAO;oBAChB,SAAS;iBACV,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAEhF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,QAAQ,QAAQ,KAAK,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACxG,IAAI,EAAE;oBACJ,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,QAAQ;oBACR,UAAU,EAAE,OAAO,CAAC,UAAU;oBAC9B,OAAO,EAAE,OAAO,CAAC,WAAW;iBAC7B;gBACD,SAAS;aACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,UAAsB;QACzD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACxD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,OAAO;oBAChB,SAAS;iBACV,CAAC;YACJ,CAAC;YAED,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;gBACrC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,oBAAoB;oBAC7B,SAAS;iBACV,CAAC;YACJ,CAAC;YAED,+BAA+B;YAC/B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,EAAE;gBACzC,WAAW,EAAE,UAAU;gBACvB,UAAU,EAAE,KAAK;aAClB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;YAEvE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE;gBAC9F,IAAI,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE;gBACjC,SAAS;aACV,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;oCAwBqB;YAC9B,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;QAE3C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE;;SAEN,KAAK,CAAC,aAAa;SACnB,KAAK,CAAC,cAAc;UACnB,KAAK,CAAC,iBAAiB,EAAE;YAC7B,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAc,EAAE,SAAkB;QACpD,IAAI,OAAO,GAAG,QAAQ,CAAC;QACvB,IAAI,IAAI,GAAG,eAAe,CAAC;QAE3B,IAAI,KAAK,YAAY,aAAa,EAAE,CAAC;YACnC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YACxB,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;aAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAClC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC1B,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,OAAO,IAAI,MAAM,OAAO,EAAE;YACnC,SAAS,EAAE,SAAS,IAAI,EAAE;SAC3B,CAAC;IACJ,CAAC;CACF"}