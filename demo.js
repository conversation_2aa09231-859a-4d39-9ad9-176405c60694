#!/usr/bin/env node

/**
 * Demo script to test Step1 MCP functionality
 */

import { ProblemSolverTool } from './dist/tools/problem-solver.js';

async function runDemo() {
  console.log('🚀 Step1 MCP Demo Starting...\n');

  const problemSolver = new ProblemSolverTool();

  try {
    // Test 1: Start a new session
    console.log('📝 Test 1: Starting new session');
    const startResponse = await problemSolver.execute({
      action: 'start',
      content: '我们团队的远程协作效率低下，经常出现沟通不畅、任务重复、进度不透明等问题，希望找到系统性的解决方案。'
    });

    console.log('✅ Start Response:', {
      success: startResponse.success,
      sessionId: startResponse.sessionId,
      nextStep: startResponse.nextStep
    });

    if (!startResponse.success) {
      console.error('❌ Failed to start session');
      return;
    }

    const sessionId = startResponse.sessionId;

    // Test 2: Get session status
    console.log('\n📊 Test 2: Getting session status');
    const statusResponse = await problemSolver.getSessionStatus(sessionId);
    console.log('✅ Status Response:', {
      success: statusResponse.success,
      currentStep: statusResponse.data?.currentStep,
      isComplete: statusResponse.data?.isComplete
    });

    // Test 3: Start step 2
    console.log('\n🔄 Test 3: Starting step 2');
    const step2Response = await problemSolver.execute({
      action: 'start',
      content: '',
      sessionId
    });

    console.log('✅ Step 2 Response:', {
      success: step2Response.success,
      requiresConfirmation: step2Response.requiresConfirmation,
      hasRestatement: !!step2Response.data?.restatement,
      rolesCount: step2Response.data?.roles?.length || 0
    });

    // Test 4: Confirm step 2
    console.log('\n✅ Test 4: Confirming step 2');
    const confirmResponse = await problemSolver.execute({
      action: 'confirm',
      content: '',
      sessionId
    });

    console.log('✅ Confirm Response:', {
      success: confirmResponse.success,
      nextStep: confirmResponse.nextStep
    });

    // Test 5: Reset to step 1
    console.log('\n🔄 Test 5: Resetting to step 1');
    const resetResponse = await problemSolver.resetToStep(sessionId, 1);
    console.log('✅ Reset Response:', {
      success: resetResponse.success,
      currentStep: resetResponse.data?.currentStep
    });

    // Test 6: Get help
    console.log('\n❓ Test 6: Getting help');
    const helpResponse = problemSolver.getHelp();
    console.log('✅ Help Response:', {
      success: helpResponse.success,
      hasMessage: !!helpResponse.message
    });

    // Test 7: Get stats
    console.log('\n📈 Test 7: Getting statistics');
    const statsResponse = problemSolver.getStats();
    console.log('✅ Stats Response:', {
      success: statsResponse.success,
      totalSessions: statsResponse.data?.totalSessions,
      activeSessions: statsResponse.data?.activeSessions,
      completedSessions: statsResponse.data?.completedSessions
    });

    // Test 8: Error handling
    console.log('\n❌ Test 8: Testing error handling');
    const errorResponse = await problemSolver.execute({
      action: 'confirm',
      content: '',
      sessionId: 'invalid-session-id'
    });

    console.log('✅ Error Response:', {
      success: errorResponse.success,
      hasErrorMessage: errorResponse.message.includes('会话未找到')
    });

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Session creation works');
    console.log('- ✅ Status checking works');
    console.log('- ✅ Step progression works');
    console.log('- ✅ Confirmation flow works');
    console.log('- ✅ Step reset works');
    console.log('- ✅ Help system works');
    console.log('- ✅ Statistics work');
    console.log('- ✅ Error handling works');

  } catch (error) {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  }
}

// Run the demo
runDemo().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
