/**
 * Basic functionality tests
 */

import { StateManager } from '../dist/core/state-manager.js';
import { InputValidator } from '../dist/core/validators.js';
import { ProblemSolverTool } from '../dist/tools/problem-solver.js';

describe('Basic Functionality', () => {
  test('StateManager creates session', () => {
    const stateManager = new StateManager();
    const session = stateManager.createSession('测试问题');
    
    expect(session.sessionId).toBeDefined();
    expect(session.userProblem).toBe('测试问题');
    expect(session.currentStep).toBe(1);
  });

  test('InputValidator validates input', () => {
    const validator = new InputValidator();
    const result = validator.validateProblemDescription('这是一个测试问题');
    
    expect(result.isValid).toBe(true);
  });

  test('ProblemSolverTool handles start action', async () => {
    const problemSolver = new ProblemSolverTool();
    const response = await problemSolver.execute({
      action: 'start',
      content: '如何提高团队协作效率？'
    });
    
    expect(response.success).toBe(true);
    expect(response.sessionId).toBeDefined();
    expect(response.nextStep).toBe(2);
  });

  test('ProblemSolverTool handles empty problem', async () => {
    const problemSolver = new ProblemSolverTool();
    const response = await problemSolver.execute({
      action: 'start',
      content: ''
    });
    
    expect(response.success).toBe(false);
    expect(response.message).toContain('问题描述不能为空');
  });

  test('ProblemSolverTool handles invalid session', async () => {
    const problemSolver = new ProblemSolverTool();
    const response = await problemSolver.execute({
      action: 'confirm',
      content: '',
      sessionId: 'invalid-session'
    });
    
    expect(response.success).toBe(false);
    expect(response.message).toContain('会话未找到');
  });

  test('Help and stats work', () => {
    const problemSolver = new ProblemSolverTool();
    
    const helpResponse = problemSolver.getHelp();
    expect(helpResponse.success).toBe(true);
    expect(helpResponse.message).toContain('使用指南');
    
    const statsResponse = problemSolver.getStats();
    expect(statsResponse.success).toBe(true);
    expect(statsResponse.data).toHaveProperty('totalSessions');
  });
});
