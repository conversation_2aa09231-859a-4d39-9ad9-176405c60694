/**
 * Integration tests for ProblemSolverTool
 */

import { ProblemSolverTool } from '../../src/tools/problem-solver.js';
import { UserInput } from '../../src/tools/types.js';

describe('ProblemSolverTool Integration', () => {
  let problemSolver: ProblemSolverTool;

  beforeEach(() => {
    problemSolver = new ProblemSolverTool();
  });

  describe('Complete workflow', () => {
    it('should complete full 5-step workflow', async () => {
      // Step 1: Start with problem
      const step1Input: UserInput = {
        action: 'start',
        content: '我们公司的远程团队协作效率低下，经常出现沟通不畅、任务重复、进度不透明等问题，希望找到系统性的解决方案来提高整体工作效率。'
      };

      const step1Response = await problemSolver.execute(step1Input);
      expect(step1Response.success).toBe(true);
      expect(step1Response.sessionId).toBeDefined();
      expect(step1Response.nextStep).toBe(2);

      const sessionId = step1Response.sessionId;

      // Step 2: Start problem restatement
      const step2StartInput: UserInput = {
        action: 'start',
        content: '',
        sessionId
      };

      const step2StartResponse = await problemSolver.execute(step2StartInput);
      expect(step2StartResponse.success).toBe(true);
      expect(step2StartResponse.requiresConfirmation).toBe(true);

      // Step 2: Confirm restatement
      const step2ConfirmInput: UserInput = {
        action: 'confirm',
        content: '',
        sessionId
      };

      const step2ConfirmResponse = await problemSolver.execute(step2ConfirmInput);
      expect(step2ConfirmResponse.success).toBe(true);
      expect(step2ConfirmResponse.nextStep).toBe(3);

      // Step 3: Start research plan
      const step3StartInput: UserInput = {
        action: 'start',
        content: '',
        sessionId
      };

      const step3StartResponse = await problemSolver.execute(step3StartInput);
      expect(step3StartResponse.success).toBe(true);
      expect(step3StartResponse.requiresConfirmation).toBe(true);

      // Step 3: Confirm plan
      const step3ConfirmInput: UserInput = {
        action: 'confirm',
        content: '',
        sessionId
      };

      const step3ConfirmResponse = await problemSolver.execute(step3ConfirmInput);
      expect(step3ConfirmResponse.success).toBe(true);
      expect(step3ConfirmResponse.nextStep).toBe(4);

      // Step 4: Start execution
      const step4StartInput: UserInput = {
        action: 'start',
        content: '',
        sessionId
      };

      const step4StartResponse = await problemSolver.execute(step4StartInput);
      expect(step4StartResponse.success).toBe(true);

      // Step 4: Confirm execution results (may need multiple confirmations)
      let currentResponse = step4StartResponse;
      while (currentResponse.requiresConfirmation && currentResponse.nextStep !== 5) {
        const confirmInput: UserInput = {
          action: 'confirm',
          content: '',
          sessionId
        };

        currentResponse = await problemSolver.execute(confirmInput);
        expect(currentResponse.success).toBe(true);
      }

      // Should now be at step 5
      expect(currentResponse.nextStep).toBe(5);

      // Step 5: Start summary generation
      const step5StartInput: UserInput = {
        action: 'start',
        content: '',
        sessionId
      };

      const step5StartResponse = await problemSolver.execute(step5StartInput);
      expect(step5StartResponse.success).toBe(true);
      expect(step5StartResponse.requiresConfirmation).toBe(true);
      expect(step5StartResponse.data?.summaries).toBeDefined();
      expect(step5StartResponse.data.summaries).toHaveLength(3);

      // Step 5: Confirm summaries
      const step5ConfirmInput: UserInput = {
        action: 'confirm',
        content: '',
        sessionId
      };

      const step5ConfirmResponse = await problemSolver.execute(step5ConfirmInput);
      expect(step5ConfirmResponse.success).toBe(true);
      expect(step5ConfirmResponse.data?.isComplete).toBe(true);
    }, 10000); // Increase timeout for integration test

    it('should handle step adjustments', async () => {
      // Start session
      const startInput: UserInput = {
        action: 'start',
        content: '如何提高销售业绩？'
      };

      const startResponse = await problemSolver.execute(startInput);
      const sessionId = startResponse.sessionId;

      // Start step 2
      const step2StartInput: UserInput = {
        action: 'start',
        content: '',
        sessionId
      };

      await problemSolver.execute(step2StartInput);

      // Adjust instead of confirm
      const adjustInput: UserInput = {
        action: 'adjust',
        content: '我更关注的是B2B销售，特别是大客户销售的策略',
        sessionId
      };

      const adjustResponse = await problemSolver.execute(adjustInput);
      expect(adjustResponse.success).toBe(true);
      expect(adjustResponse.requiresConfirmation).toBe(true);
      expect(adjustResponse.message).toContain('调整后的问题重述');
    });

    it('should handle regeneration in step 5', async () => {
      // Quick path to step 5 (simplified for testing)
      const startInput: UserInput = {
        action: 'start',
        content: '测试问题'
      };

      const startResponse = await problemSolver.execute(startInput);
      const sessionId = startResponse.sessionId;

      // Manually advance to step 5 for testing
      await problemSolver.resetToStep(sessionId, 5);

      const step5StartInput: UserInput = {
        action: 'start',
        content: '',
        sessionId
      };

      await problemSolver.execute(step5StartInput);

      // Request regeneration
      const regenerateInput: UserInput = {
        action: 'regenerate',
        content: '请更加关注技术实现方面',
        sessionId
      };

      const regenerateResponse = await problemSolver.execute(regenerateInput);
      expect(regenerateResponse.success).toBe(true);
      expect(regenerateResponse.requiresConfirmation).toBe(true);
      expect(regenerateResponse.message).toContain('重新生成的总结');
    });
  });

  describe('Error handling', () => {
    it('should handle invalid session ID', async () => {
      const input: UserInput = {
        action: 'confirm',
        content: '',
        sessionId: 'invalid-session-id'
      };

      const response = await problemSolver.execute(input);
      expect(response.success).toBe(false);
      expect(response.message).toContain('会话未找到');
    });

    it('should handle empty problem description', async () => {
      const input: UserInput = {
        action: 'start',
        content: ''
      };

      const response = await problemSolver.execute(input);
      expect(response.success).toBe(false);
      expect(response.message).toContain('问题描述不能为空');
    });

    it('should handle invalid actions for steps', async () => {
      const startInput: UserInput = {
        action: 'start',
        content: '测试问题'
      };

      const startResponse = await problemSolver.execute(startInput);
      const sessionId = startResponse.sessionId;

      // Try invalid action for step 2
      const invalidInput: UserInput = {
        action: 'answer', // Invalid for step 2
        content: '无效操作',
        sessionId
      };

      const response = await problemSolver.execute(invalidInput);
      expect(response.success).toBe(false);
    });
  });

  describe('Session management', () => {
    it('should get session status', async () => {
      const startInput: UserInput = {
        action: 'start',
        content: '测试问题'
      };

      const startResponse = await problemSolver.execute(startInput);
      const sessionId = startResponse.sessionId;

      const statusResponse = await problemSolver.getSessionStatus(sessionId);
      expect(statusResponse.success).toBe(true);
      expect(statusResponse.data?.currentStep).toBe(1);
      expect(statusResponse.data?.isComplete).toBe(false);
    });

    it('should reset to specific step', async () => {
      const startInput: UserInput = {
        action: 'start',
        content: '测试问题'
      };

      const startResponse = await problemSolver.execute(startInput);
      const sessionId = startResponse.sessionId;

      const resetResponse = await problemSolver.resetToStep(sessionId, 3);
      expect(resetResponse.success).toBe(true);
      expect(resetResponse.data?.currentStep).toBe(3);

      const statusResponse = await problemSolver.getSessionStatus(sessionId);
      expect(statusResponse.data?.currentStep).toBe(3);
    });

    it('should get help information', () => {
      const helpResponse = problemSolver.getHelp();
      expect(helpResponse.success).toBe(true);
      expect(helpResponse.message).toContain('问题解决助手使用指南');
    });

    it('should get statistics', () => {
      const statsResponse = problemSolver.getStats();
      expect(statsResponse.success).toBe(true);
      expect(statsResponse.data).toHaveProperty('totalSessions');
      expect(statsResponse.data).toHaveProperty('activeSessions');
      expect(statsResponse.data).toHaveProperty('completedSessions');
    });
  });
});
