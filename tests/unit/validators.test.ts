/**
 * Unit tests for InputValidator
 */

import { InputValidator } from '../../src/core/validators.js';
import { UserInput } from '../../src/tools/types.js';

describe('InputValidator', () => {
  let validator: InputValidator;

  beforeEach(() => {
    validator = new InputValidator();
  });

  describe('validateInput', () => {
    it('should validate valid start action', () => {
      const input: UserInput = {
        action: 'start',
        content: '如何提高团队协作效率？'
      };

      const result = validator.validateInput(input);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty input', () => {
      const result = validator.validateInput(null as any);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('输入不能为空');
    });

    it('should reject invalid action', () => {
      const input: UserInput = {
        action: 'invalid' as any,
        content: '测试内容'
      };

      const result = validator.validateInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('无效的操作类型: invalid');
    });

    it('should require content for non-start actions', () => {
      const input: UserInput = {
        action: 'confirm',
        content: ''
      };

      const result = validator.validateInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('操作内容不能为空');
    });

    it('should require sessionId for non-start actions', () => {
      const input: UserInput = {
        action: 'confirm',
        content: '确认'
      };

      const result = validator.validateInput(input);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('缺少会话ID');
    });

    it('should warn about long content', () => {
      const longContent = 'a'.repeat(5001);
      const input: UserInput = {
        action: 'start',
        content: longContent
      };

      const result = validator.validateInput(input);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('输入内容较长，建议简化描述');
    });
  });

  describe('validateProblemDescription', () => {
    it('should validate good problem description', () => {
      const description = '我们团队在项目协作中遇到沟通效率低的问题，希望找到改善方案';
      const result = validator.validateProblemDescription(description);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty description', () => {
      const result = validator.validateProblemDescription('');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('问题描述不能为空');
    });

    it('should warn about short description', () => {
      const result = validator.validateProblemDescription('短描述');

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('问题描述过于简短，建议提供更多细节');
    });

    it('should warn about long description', () => {
      const longDescription = 'a'.repeat(2001);
      const result = validator.validateProblemDescription(longDescription);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('问题描述过长，建议精简表达');
    });

    it('should warn if no question words', () => {
      const result = validator.validateProblemDescription('这是一个普通的描述文本');

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('建议明确说明具体问题或挑战');
    });

    it('should warn if no goal words', () => {
      const result = validator.validateProblemDescription('遇到了很多问题和困难');

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('建议说明期望达到的目标');
    });
  });

  describe('validateConfirmation', () => {
    it('should validate confirm action', () => {
      const result = validator.validateConfirmation('confirm', ['confirm', 'adjust']);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate adjust action', () => {
      const result = validator.validateConfirmation('adjust', ['confirm', 'adjust']);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty input', () => {
      const result = validator.validateConfirmation('', ['confirm', 'adjust']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('请选择操作：confirm（确认）或 adjust（调整）');
    });

    it('should reject invalid action', () => {
      const result = validator.validateConfirmation('invalid', ['confirm', 'adjust']);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('无效操作，请选择：confirm、adjust');
    });
  });

  describe('validateAdjustmentFeedback', () => {
    it('should validate good feedback', () => {
      const result = validator.validateAdjustmentFeedback('需要更加关注技术实现方面');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty feedback', () => {
      const result = validator.validateAdjustmentFeedback('');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('请说明需要调整的具体内容');
    });

    it('should warn about short feedback', () => {
      const result = validator.validateAdjustmentFeedback('短');

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('调整说明过于简短，建议提供更多细节');
    });
  });

  describe('validateAnswer', () => {
    it('should validate good answer', () => {
      const result = validator.validateAnswer('这是一个详细的回答', 1);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject empty answer', () => {
      const result = validator.validateAnswer('', 1);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('回答不能为空');
    });

    it('should warn about short answer', () => {
      const result = validator.validateAnswer('是', 1);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('回答过于简短，建议提供更多信息');
    });

    it('should warn about too many questions', () => {
      const result = validator.validateAnswer('回答内容', 5);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('问题数量较多(5)，建议重点回答关键问题');
    });
  });

  describe('validateSessionId', () => {
    it('should validate correct session ID format', () => {
      const sessionId = 'session_1234567890_abc123def';
      const result = validator.validateSessionId(sessionId);

      expect(result).toBe(true);
    });

    it('should reject invalid session ID format', () => {
      const invalidIds = [
        '',
        'invalid-id',
        'session_abc_123',
        'session_123_',
        'session__abc123'
      ];

      invalidIds.forEach(id => {
        expect(validator.validateSessionId(id)).toBe(false);
      });
    });
  });

  describe('sanitizeInput', () => {
    it('should trim whitespace', () => {
      const result = validator.sanitizeInput('  测试内容  ');

      expect(result).toBe('测试内容');
    });

    it('should replace multiple spaces with single space', () => {
      const result = validator.sanitizeInput('测试   内容   文本');

      expect(result).toBe('测试 内容 文本');
    });

    it('should remove HTML tags', () => {
      const result = validator.sanitizeInput('测试<script>alert("xss")</script>内容');

      expect(result).toBe('测试alert("xss")内容');
    });

    it('should limit length', () => {
      const longText = 'a'.repeat(6000);
      const result = validator.sanitizeInput(longText);

      expect(result.length).toBe(5000);
    });

    it('should handle empty input', () => {
      const result = validator.sanitizeInput('');

      expect(result).toBe('');
    });

    it('should handle null input', () => {
      const result = validator.sanitizeInput(null as any);

      expect(result).toBe('');
    });
  });

  describe('getValidationSummary', () => {
    it('should format errors and warnings', () => {
      const result = {
        isValid: false,
        errors: ['错误1', '错误2'],
        warnings: ['警告1', '警告2']
      };

      const summary = validator.getValidationSummary(result);

      expect(summary).toContain('错误：错误1；错误2');
      expect(summary).toContain('提醒：警告1；警告2');
    });

    it('should handle only errors', () => {
      const result = {
        isValid: false,
        errors: ['错误1'],
        warnings: []
      };

      const summary = validator.getValidationSummary(result);

      expect(summary).toBe('错误：错误1');
    });

    it('should handle only warnings', () => {
      const result = {
        isValid: true,
        errors: [],
        warnings: ['警告1']
      };

      const summary = validator.getValidationSummary(result);

      expect(summary).toBe('提醒：警告1');
    });

    it('should handle no errors or warnings', () => {
      const result = {
        isValid: true,
        errors: [],
        warnings: []
      };

      const summary = validator.getValidationSummary(result);

      expect(summary).toBe('');
    });
  });
});
