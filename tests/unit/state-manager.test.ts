/**
 * Unit tests for StateManager
 */

import { StateManager } from '../../src/core/state-manager.js';
import { StepNumber } from '../../src/tools/types.js';

describe('StateManager', () => {
  let stateManager: StateManager;

  beforeEach(() => {
    stateManager = new StateManager();
  });

  describe('createSession', () => {
    it('should create a new session with valid problem', () => {
      const problem = '如何提高团队协作效率？';
      const session = stateManager.createSession(problem);

      expect(session.sessionId).toBeDefined();
      expect(session.userProblem).toBe(problem);
      expect(session.currentStep).toBe(1);
      expect(session.isComplete).toBe(false);
      expect(session.createdAt).toBeInstanceOf(Date);
      expect(session.updatedAt).toBeInstanceOf(Date);
    });

    it('should trim whitespace from problem', () => {
      const problem = '  如何提高团队协作效率？  ';
      const session = stateManager.createSession(problem);

      expect(session.userProblem).toBe('如何提高团队协作效率？');
    });

    it('should generate unique session IDs', () => {
      const session1 = stateManager.createSession('问题1');
      const session2 = stateManager.createSession('问题2');

      expect(session1.sessionId).not.toBe(session2.sessionId);
    });
  });

  describe('getSession', () => {
    it('should return session if exists', () => {
      const session = stateManager.createSession('测试问题');
      const retrieved = stateManager.getSession(session.sessionId);

      expect(retrieved).toEqual(session);
    });

    it('should return null if session does not exist', () => {
      const retrieved = stateManager.getSession('non-existent-id');

      expect(retrieved).toBeNull();
    });
  });

  describe('updateSession', () => {
    it('should update session properties', () => {
      const session = stateManager.createSession('测试问题');
      const updatedSession = stateManager.updateSession(session.sessionId, {
        restatement: '重新表述的问题',
        roles: ['角色1', '角色2']
      });

      expect(updatedSession.restatement).toBe('重新表述的问题');
      expect(updatedSession.roles).toEqual(['角色1', '角色2']);
      expect(updatedSession.updatedAt.getTime()).toBeGreaterThan(session.createdAt.getTime());
    });

    it('should throw error if session does not exist', () => {
      expect(() => {
        stateManager.updateSession('non-existent-id', { restatement: 'test' });
      }).toThrow('Session non-existent-id not found');
    });
  });

  describe('advanceStep', () => {
    it('should advance to next step', () => {
      const session = stateManager.createSession('测试问题');
      const advanced = stateManager.advanceStep(session.sessionId);

      expect(advanced.currentStep).toBe(2);
    });

    it('should throw error if already at final step', () => {
      const session = stateManager.createSession('测试问题');
      // Advance to step 5
      stateManager.updateSession(session.sessionId, { currentStep: 5 });

      expect(() => {
        stateManager.advanceStep(session.sessionId);
      }).toThrow('Already at final step');
    });

    it('should throw error if session does not exist', () => {
      expect(() => {
        stateManager.advanceStep('non-existent-id');
      }).toThrow('Session non-existent-id not found');
    });
  });

  describe('goBackStep', () => {
    it('should go back to previous step', () => {
      const session = stateManager.createSession('测试问题');
      stateManager.updateSession(session.sessionId, { currentStep: 3 });
      
      const backStep = stateManager.goBackStep(session.sessionId);

      expect(backStep.currentStep).toBe(2);
    });

    it('should throw error if already at first step', () => {
      const session = stateManager.createSession('测试问题');

      expect(() => {
        stateManager.goBackStep(session.sessionId);
      }).toThrow('Already at first step');
    });
  });

  describe('completeSession', () => {
    it('should mark session as complete', () => {
      const session = stateManager.createSession('测试问题');
      const completed = stateManager.completeSession(session.sessionId);

      expect(completed.isComplete).toBe(true);
      expect(completed.currentStep).toBe(5);
    });
  });

  describe('canAdvanceToStep', () => {
    it('should return true if step 1 has problem', () => {
      const session = stateManager.createSession('测试问题');
      const canAdvance = stateManager.canAdvanceToStep(session.sessionId, 2);

      expect(canAdvance).toBe(true);
    });

    it('should return false if step 2 missing restatement', () => {
      const session = stateManager.createSession('测试问题');
      stateManager.updateSession(session.sessionId, { currentStep: 2 });
      
      const canAdvance = stateManager.canAdvanceToStep(session.sessionId, 3);

      expect(canAdvance).toBe(false);
    });

    it('should return true if step 2 has restatement and roles', () => {
      const session = stateManager.createSession('测试问题');
      stateManager.updateSession(session.sessionId, { 
        currentStep: 2,
        restatement: '重述',
        roles: ['角色1']
      });
      
      const canAdvance = stateManager.canAdvanceToStep(session.sessionId, 3);

      expect(canAdvance).toBe(true);
    });
  });

  describe('getStats', () => {
    it('should return correct statistics', () => {
      const session1 = stateManager.createSession('问题1');
      const session2 = stateManager.createSession('问题2');
      stateManager.completeSession(session1.sessionId);

      const stats = stateManager.getStats();

      expect(stats.totalSessions).toBe(2);
      expect(stats.activeSessions).toBe(1);
      expect(stats.completedSessions).toBe(1);
    });
  });

  describe('deleteSession', () => {
    it('should delete session successfully', () => {
      const session = stateManager.createSession('测试问题');
      const deleted = stateManager.deleteSession(session.sessionId);

      expect(deleted).toBe(true);
      expect(stateManager.getSession(session.sessionId)).toBeNull();
    });

    it('should return false if session does not exist', () => {
      const deleted = stateManager.deleteSession('non-existent-id');

      expect(deleted).toBe(false);
    });
  });
});
