/**
 * State Manager for Step1 MCP
 * Manages session state and step transitions
 */

import { SessionState, StepN<PERSON>ber, Step1MCPError, DEFAULT_CONFIG } from '../tools/types.js';

export class StateManager {
  private sessions: Map<string, SessionState> = new Map();
  private readonly config = DEFAULT_CONFIG;

  /**
   * Create a new session
   */
  createSession(userProblem: string): SessionState {
    const sessionId = this.generateSessionId();
    const now = new Date();
    
    const session: SessionState = {
      currentStep: 1,
      userProblem: userProblem.trim(),
      restatement: '',
      roles: [],
      researchPlan: {
        title: '',
        description: '',
        steps: [],
        estimatedDuration: '',
        requiredRoles: []
      },
      executionResults: [],
      summaries: [],
      isComplete: false,
      sessionId,
      createdAt: now,
      updatedAt: now
    };

    this.sessions.set(sessionId, session);
    this.scheduleCleanup(sessionId);
    
    return session;
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): SessionState | null {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Update session state
   */
  updateSession(sessionId: string, updates: Partial<SessionState>): SessionState {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Step1MCPError(`Session ${sessionId} not found`, 'SESSION_NOT_FOUND');
    }

    const updatedSession = {
      ...session,
      ...updates,
      updatedAt: new Date()
    };

    this.sessions.set(sessionId, updatedSession);
    return updatedSession;
  }

  /**
   * Advance to next step
   */
  advanceStep(sessionId: string): SessionState {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Step1MCPError(`Session ${sessionId} not found`, 'SESSION_NOT_FOUND');
    }

    if (session.currentStep >= 5) {
      throw new Step1MCPError('Already at final step', 'INVALID_STEP_TRANSITION');
    }

    const nextStep = (session.currentStep + 1) as StepNumber;
    return this.updateSession(sessionId, { currentStep: nextStep });
  }

  /**
   * Go back to previous step
   */
  goBackStep(sessionId: string): SessionState {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Step1MCPError(`Session ${sessionId} not found`, 'SESSION_NOT_FOUND');
    }

    if (session.currentStep <= 1) {
      throw new Step1MCPError('Already at first step', 'INVALID_STEP_TRANSITION');
    }

    const prevStep = (session.currentStep - 1) as StepNumber;
    return this.updateSession(sessionId, { currentStep: prevStep });
  }

  /**
   * Mark session as complete
   */
  completeSession(sessionId: string): SessionState {
    return this.updateSession(sessionId, { 
      isComplete: true,
      currentStep: 5
    });
  }

  /**
   * Delete session
   */
  deleteSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): SessionState[] {
    return Array.from(this.sessions.values()).filter(s => !s.isComplete);
  }

  /**
   * Validate step transition
   */
  canAdvanceToStep(sessionId: string, _targetStep: StepNumber): boolean {
    const session = this.getSession(sessionId);
    if (!session) return false;

    // Check if required data for current step is complete
    switch (session.currentStep) {
      case 1:
        return session.userProblem.length > 0;
      case 2:
        return session.restatement.length > 0 && session.roles.length > 0;
      case 3:
        return session.researchPlan.steps.length > 0;
      case 4:
        return session.executionResults.length > 0;
      case 5:
        return session.summaries.length >= 3;
      default:
        return false;
    }
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Schedule session cleanup after timeout
   */
  private scheduleCleanup(sessionId: string): void {
    const timeoutMs = this.config.sessionTimeoutMinutes * 60 * 1000;
    
    setTimeout(() => {
      const session = this.getSession(sessionId);
      if (session && !session.isComplete) {
        this.deleteSession(sessionId);
        console.log(`Session ${sessionId} cleaned up due to timeout`);
      }
    }, timeoutMs);
  }

  /**
   * Get session statistics
   */
  getStats(): {
    totalSessions: number;
    activeSessions: number;
    completedSessions: number;
  } {
    const allSessions = Array.from(this.sessions.values());
    return {
      totalSessions: allSessions.length,
      activeSessions: allSessions.filter(s => !s.isComplete).length,
      completedSessions: allSessions.filter(s => s.isComplete).length
    };
  }
}
