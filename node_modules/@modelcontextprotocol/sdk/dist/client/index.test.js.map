{"version": 3, "file": "index.test.js", "sourceRoot": "", "sources": ["../../src/client/index.test.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,kDAAkD;AAClD,6DAA6D;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,aAAa,EAAE,kBAAkB,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAE9E;;EAEE;AACF,IAAI,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAC5B,MAAM,uBAAuB,GAAG,aAAa,CAAC,MAAM,CAAC;QACnD,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;QAChC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;YACf,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;SACjB,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,wBAAwB,GAAG,aAAa,CAAC,MAAM,CAAC;QACpD,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC;QACrC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;YACf,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;YAChB,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;SACjB,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,iCAAiC,GAAG,kBAAkB,CAAC,MAAM,CAAC;QAClE,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;QAClC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;YACf,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACtC,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE;SACpB,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,EAAE,CACrD,wBAAwB,CACzB,CAAC;IACF,MAAM,yBAAyB,GAAG,iCAAiC,CAAC;IACpE,MAAM,mBAAmB,GAAG,YAAY,CAAC,MAAM,CAAC;QAC9C,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;QACvB,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE;KACvB,CAAC,CAAC;IAMH,yCAAyC;IACzC,MAAM,aAAa,GAAG,IAAI,MAAM,CAI9B;QACA,IAAI,EAAE,eAAe;QACrB,OAAO,EAAE,OAAO;KACjB,CAAC,CAAC;IAEH,+EAA+E;IAC/E,KAAK;QACH,aAAa,CAAC,OAAO,CACnB;YACE,MAAM,EAAE,aAAa;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,SAAS;aAChB;SACF,EACD,mBAAmB,CACpB,CAAC;IAEJ,KAAK;QACH,aAAa,CAAC,YAAY,CAAC;YACzB,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE;gBACN,QAAQ,EAAE,SAAS;gBACnB,OAAO,EAAE,mBAAmB;aAC7B;SACF,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}