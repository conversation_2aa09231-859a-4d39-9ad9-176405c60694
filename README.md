# Step1 MCP - 多步骤问题解决助手

一个基于模型上下文协议（MCP）的包，提供结构化的5步交互流程来解决问题并生成概念方案。

## 功能特性

- **步骤1**: 问题输入 - 用户输入需要解决的问题或挑战
- **步骤2**: 问题重述与角色识别 - AI重述问题并建议相关专业角色
- **步骤3**: 研究计划制定 - 基于问题和角色创建详细的研究计划
- **步骤4**: 计划执行与确认 - 逐步执行计划并在关键节点确认
- **步骤5**: 总结生成 - 生成3个不同角度的综合总结并确认满意度

## 安装

```bash
npm install step1-mcp
```

## 配置

将以下配置添加到您的MCP客户端配置中：

```json
{
  "mcpServers": {
    "step1-mcp": {
      "command": "node",
      "args": ["path/to/step1-mcp/dist/index.js"]
    }
  }
}
```

## 使用方法

### 基本使用

该包提供了一个名为 `problem_solver` 的MCP工具，引导用户完成5步流程：

#### 1. 开始新会话

```json
{
  "action": "start",
  "content": "我们公司的远程团队协作效率低下，经常出现沟通不畅、任务重复、进度不透明等问题，希望找到系统性的解决方案。"
}
```

#### 2. 确认问题重述

```json
{
  "action": "confirm",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

或调整问题重述：

```json
{
  "action": "adjust",
  "content": "我更关注技术团队的协作，特别是代码协作和版本管理方面",
  "sessionId": "session_1234567890_abc123"
}
```

#### 3. 确认研究计划

```json
{
  "action": "confirm",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

#### 4. 执行计划并确认关键步骤

```json
{
  "action": "confirm",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

回答问题：

```json
{
  "action": "answer",
  "content": "我们主要使用Git进行版本控制，团队规模约20人",
  "sessionId": "session_1234567890_abc123"
}
```

#### 5. 确认或重新生成总结

```json
{
  "action": "confirm",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

重新生成总结：

```json
{
  "action": "regenerate",
  "content": "请更加关注技术实现和工具推荐",
  "sessionId": "session_1234567890_abc123"
}
```

### 辅助工具

#### 获取会话状态

```json
{
  "tool": "get_session_status",
  "sessionId": "session_1234567890_abc123"
}
```

#### 重置到指定步骤

```json
{
  "tool": "reset_to_step",
  "sessionId": "session_1234567890_abc123",
  "step": 3
}
```

#### 获取帮助信息

```json
{
  "tool": "get_help"
}
```

#### 获取统计信息

```json
{
  "tool": "get_stats"
}
```

## 开发

```bash
# 安装依赖
npm install

# 构建项目
npm run build

# 开发模式
npm run dev

# 运行测试
npm test

# 代码检查
npm run lint
```

## 项目结构

```
src/
├── index.ts              # MCP服务器入口
├── tools/                # MCP工具定义
│   ├── problem-solver.ts # 主要交互工具
│   └── types.ts          # 类型定义
├── core/                 # 核心业务逻辑
│   ├── state-manager.ts  # 状态管理
│   ├── step-handlers.ts  # 各步骤处理器
│   └── validators.ts     # 输入验证
└── utils/                # 工具函数
    ├── prompts.ts        # 提示词模板
    └── helpers.ts        # 辅助函数
```

## 设计原则

### 渐进式交互
- 每次只展示当前步骤的信息
- 避免一次性输出所有步骤
- 每次提问不超过3个问题

### 用户确认机制
- 关键步骤需要用户确认
- 支持调整和重新生成
- 提供清晰的操作指引

### 状态管理
- 会话状态持久化
- 支持步骤回退
- 自动清理超时会话

## 示例场景

### 场景1：技术问题解决

**问题输入：**
"我们的微服务架构中服务间通信延迟过高，影响用户体验，需要优化方案"

**AI重述：**
"您的核心问题是微服务架构中的服务间通信性能优化..."

**建议角色：**
- 系统架构师
- 性能优化专家
- DevOps工程师

### 场景2：业务流程改进

**问题输入：**
"公司的客户服务流程效率低，客户满意度下降，需要重新设计流程"

**AI重述：**
"您希望改进客户服务流程以提高效率和客户满意度..."

**建议角色：**
- 业务流程分析师
- 客户体验专家
- 运营管理专家

## 错误处理

### 常见错误

1. **会话未找到**
   - 错误码：SESSION_NOT_FOUND
   - 解决方案：重新开始会话

2. **无效操作**
   - 错误码：INVALID_ACTION
   - 解决方案：检查当前步骤支持的操作

3. **输入验证失败**
   - 错误码：VALIDATION_ERROR
   - 解决方案：根据错误信息调整输入

### 调试技巧

1. 使用 `get_session_status` 查看当前状态
2. 检查错误消息中的具体指导
3. 使用 `get_help` 获取使用指南

## 性能考虑

- 会话自动超时清理（默认60分钟）
- 内存状态管理，重启后会话丢失
- 建议生产环境使用持久化存储

## 扩展开发

### 添加新的处理步骤

1. 在 `types.ts` 中定义新的步骤类型
2. 在 `step-handlers.ts` 中实现处理逻辑
3. 更新 `problem-solver.ts` 中的路由

### 自定义提示词模板

1. 修改 `prompts.ts` 中的模板
2. 支持变量替换和多语言

### 集成外部AI服务

1. 在步骤处理器中调用外部API
2. 处理异步操作和错误重试

## 许可证

MIT

## 贡献

欢迎提交问题报告和功能请求。请阅读贡献指南并提交拉取请求来帮助改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现5步问题解决流程
- 支持MCP协议集成
- 完整的测试覆盖
