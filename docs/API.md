# Step1 MCP API 文档

## 概述

Step1 MCP 提供了一套完整的API来支持5步问题解决流程。所有的交互都通过MCP协议进行。

## 工具列表

### 1. problem_solver

主要的问题解决工具，支持完整的5步流程。

#### 输入参数

```typescript
interface UserInput {
  action: 'start' | 'confirm' | 'adjust' | 'answer' | 'regenerate';
  content: string;
  sessionId?: string;
}
```

#### 参数说明

- **action**: 操作类型
  - `start`: 开始新会话或新步骤
  - `confirm`: 确认当前步骤结果
  - `adjust`: 调整当前步骤内容
  - `answer`: 回答问题（步骤4专用）
  - `regenerate`: 重新生成（步骤5专用）

- **content**: 操作内容
  - 对于 `start` 操作：问题描述
  - 对于 `adjust` 操作：调整说明
  - 对于 `answer` 操作：问题答案
  - 对于 `regenerate` 操作：重新生成要求

- **sessionId**: 会话ID（start操作时可选，其他操作必需）

#### 返回格式

```typescript
interface ToolResponse {
  success: boolean;
  message: string;
  data?: any;
  nextStep?: StepNumber;
  requiresConfirmation?: boolean;
  questions?: string[];
  sessionId: string;
}
```

#### 各步骤详细说明

##### 步骤1：问题输入

**请求示例：**
```json
{
  "action": "start",
  "content": "我们团队的项目管理效率低下，经常出现任务重复、进度不透明、沟通不畅等问题"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "问题已记录：我们团队的项目管理效率低下...\n\n现在我将进入第二步，对您的问题进行重述并识别需要的专业角色。",
  "data": {
    "problem": "我们团队的项目管理效率低下...",
    "sessionId": "session_1234567890_abc123"
  },
  "nextStep": 2,
  "sessionId": "session_1234567890_abc123"
}
```

##### 步骤2：问题重述与角色识别

**开始步骤2：**
```json
{
  "action": "start",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

**确认重述：**
```json
{
  "action": "confirm",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

**调整重述：**
```json
{
  "action": "adjust",
  "content": "我更关注技术团队的项目管理，特别是敏捷开发流程",
  "sessionId": "session_1234567890_abc123"
}
```

##### 步骤3：研究计划制定

**开始步骤3：**
```json
{
  "action": "start",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

**响应包含详细计划：**
```json
{
  "success": true,
  "message": "**研究计划：项目管理优化方案**\n\n...",
  "data": {
    "researchPlan": {
      "title": "项目管理优化方案",
      "description": "针对团队项目管理效率问题的系统性解决方案",
      "steps": [...],
      "estimatedDuration": "约2小时",
      "requiredRoles": ["项目管理专家", "敏捷教练", "团队协作顾问"]
    }
  },
  "requiresConfirmation": true,
  "sessionId": "session_1234567890_abc123"
}
```

##### 步骤4：计划执行与确认

**开始执行：**
```json
{
  "action": "start",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

**确认关键步骤：**
```json
{
  "action": "confirm",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

**回答问题：**
```json
{
  "action": "answer",
  "content": "我们团队使用Jira进行任务管理，团队规模约15人",
  "sessionId": "session_1234567890_abc123"
}
```

##### 步骤5：总结生成

**开始生成总结：**
```json
{
  "action": "start",
  "content": "",
  "sessionId": "session_1234567890_abc123"
}
```

**重新生成总结：**
```json
{
  "action": "regenerate",
  "content": "请更加关注技术实现和工具推荐",
  "sessionId": "session_1234567890_abc123"
}
```

### 2. get_session_status

获取当前会话的状态和进度。

#### 输入参数

```json
{
  "sessionId": "session_1234567890_abc123"
}
```

#### 返回示例

```json
{
  "success": true,
  "message": "当前状态：问题重述与角色识别\n●○○○○ (2/5)\n\n请确认这个重述是否准确反映了您想要解决的问题？如需调整，请告诉我。",
  "data": {
    "currentStep": 2,
    "stepName": "问题重述与角色识别",
    "isComplete": false,
    "problem": "我们团队的项目管理效率低下..."
  },
  "sessionId": "session_1234567890_abc123"
}
```

### 3. reset_to_step

重置会话到指定步骤。

#### 输入参数

```json
{
  "sessionId": "session_1234567890_abc123",
  "step": 3
}
```

#### 返回示例

```json
{
  "success": true,
  "message": "已重置到研究计划制定\n●●○○○ (3/5)\n\n请审查研究计划，确认步骤安排和时间估算是否合理。",
  "data": {
    "currentStep": 3
  },
  "sessionId": "session_1234567890_abc123"
}
```

### 4. get_help

获取使用帮助和指南。

#### 输入参数

```json
{}
```

#### 返回示例

```json
{
  "success": true,
  "message": "**问题解决助手使用指南**\n\n**开始新会话：**\n...",
  "sessionId": ""
}
```

### 5. get_stats

获取系统统计信息。

#### 输入参数

```json
{}
```

#### 返回示例

```json
{
  "success": true,
  "message": "**系统统计信息**\n\n- 总会话数：10\n- 活跃会话：3\n- 已完成会话：7",
  "data": {
    "totalSessions": 10,
    "activeSessions": 3,
    "completedSessions": 7
  },
  "sessionId": ""
}
```

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "message": "错误 (ERROR_CODE): 错误描述",
  "sessionId": "session_id_or_empty"
}
```

### 常见错误码

- `SESSION_NOT_FOUND`: 会话未找到
- `INVALID_ACTION`: 无效的操作类型
- `VALIDATION_ERROR`: 输入验证失败
- `INVALID_STEP`: 无效的步骤操作
- `MISSING_DATA`: 缺少必要数据

## 最佳实践

1. **会话管理**
   - 保存sessionId用于后续操作
   - 定期检查会话状态
   - 处理会话超时情况

2. **错误处理**
   - 检查响应的success字段
   - 根据错误消息提供用户友好的提示
   - 实现重试机制

3. **用户体验**
   - 显示进度指示器
   - 提供清晰的操作指引
   - 支持步骤回退和调整

4. **性能优化**
   - 避免频繁的状态查询
   - 缓存会话信息
   - 处理长时间操作的超时
