# Step1 MCP 使用示例

## 完整流程示例

### 示例1：技术团队协作优化

#### 步骤1：问题输入

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "start",
    "content": "我们的技术团队在远程协作中遇到了严重问题：代码冲突频繁、代码审查效率低、技术债务积累、团队成员技能水平不一致，导致项目交付延期和质量问题。希望建立一套完整的技术协作流程和规范。"
  }
}
```

**响应：**
```json
{
  "success": true,
  "message": "问题已记录：我们的技术团队在远程协作中遇到了严重问题...\n\n现在我将进入第二步，对您的问题进行重述并识别需要的专业角色。\n\n进度：●○○○○ (1/5)",
  "data": {
    "problem": "我们的技术团队在远程协作中遇到了严重问题...",
    "sessionId": "session_1703123456_abc123def"
  },
  "nextStep": 2,
  "sessionId": "session_1703123456_abc123def"
}
```

#### 步骤2：问题重述与角色识别

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "start",
    "content": "",
    "sessionId": "session_1703123456_abc123def"
  }
}
```

**响应：**
```json
{
  "success": true,
  "message": "**问题重述：**\n基于您的描述，我理解您的核心问题是：技术团队远程协作效率和质量问题。这个问题涉及代码管理、团队协作流程、技能提升和质量保障等多个方面的考虑，需要系统性的分析和解决方案设计。\n\n**建议的专业角色：**\n1. **Problem Analyst**: 分析和分解复杂问题\n2. **Solution Architect**: 设计综合解决方案\n3. **Implementation Specialist**: 专注于实际实施\n4. **Technical Expert**: 技术专家，专注于技术解决方案\n\n请确认这个重述是否准确反映了您想要解决的问题？如需调整，请告诉我。",
  "data": {
    "restatement": "基于您的描述，我理解您的核心问题是：技术团队远程协作效率和质量问题...",
    "roles": [
      {
        "name": "Problem Analyst",
        "description": "分析和分解复杂问题"
      },
      {
        "name": "Solution Architect", 
        "description": "设计综合解决方案"
      },
      {
        "name": "Implementation Specialist",
        "description": "专注于实际实施"
      },
      {
        "name": "Technical Expert",
        "description": "技术专家，专注于技术解决方案"
      }
    ]
  },
  "requiresConfirmation": true,
  "sessionId": "session_1703123456_abc123def"
}
```

**确认重述：**
```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "confirm",
    "content": "",
    "sessionId": "session_1703123456_abc123def"
  }
}
```

#### 步骤3：研究计划制定

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "start",
    "content": "",
    "sessionId": "session_1703123456_abc123def"
  }
}
```

**响应：**
```json
{
  "success": true,
  "message": "**研究计划：问题解决研究计划**\n\n针对\"技术团队远程协作效率和质量问题\"的系统性解决方案研究计划\n\n**执行步骤：**\n1. **问题分析与需求收集** (Problem Analyst)\n   深入分析问题的各个方面，收集详细需求\n   预计时间：30分钟 [需确认]\n\n2. **解决方案设计** (Solution Architect)\n   基于分析结果设计可行的解决方案\n   预计时间：45分钟 [需确认]\n\n3. **实施计划制定** (Implementation Specialist)\n   制定详细的实施计划和时间表\n   预计时间：30分钟\n\n**总预计时间：** 约2小时\n\n这个计划是否需要调整？请告诉我您的想法。",
  "data": {
    "researchPlan": {
      "title": "问题解决研究计划",
      "description": "针对\"技术团队远程协作效率和质量问题\"的系统性解决方案研究计划",
      "steps": [...],
      "estimatedDuration": "约2小时",
      "requiredRoles": ["Problem Analyst", "Solution Architect", "Implementation Specialist", "Technical Expert"]
    }
  },
  "requiresConfirmation": true,
  "sessionId": "session_1703123456_abc123def"
}
```

#### 步骤4：计划执行

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "start",
    "content": "",
    "sessionId": "session_1703123456_abc123def"
  }
}
```

**执行第一步并确认：**
```json
{
  "success": true,
  "message": "**执行步骤：问题分析与需求收集**\n\n已完成问题分析与需求收集：\n\n基于Problem Analyst的专业视角，我们分析了相关问题并得出了初步结论。具体包括：\n1. 关键发现和洞察\n2. 潜在解决方案\n3. 风险评估\n4. 下一步建议\n\n这是一个关键步骤，请确认结果是否满意？如有疑问，我可以为您解答。",
  "data": {
    "result": {
      "stepId": "step_1",
      "status": "completed",
      "result": "已完成问题分析与需求收集...",
      "questions": [
        "关于问题分析与需求收集，您是否有特殊要求？",
        "在深入分析问题的各个方面，收集详细需求过程中，有什么需要特别注意的吗？"
      ]
    }
  },
  "requiresConfirmation": true,
  "questions": [
    "关于问题分析与需求收集，您是否有特殊要求？",
    "在深入分析问题的各个方面，收集详细需求过程中，有什么需要特别注意的吗？"
  ],
  "sessionId": "session_1703123456_abc123def"
}
```

**回答问题：**
```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "answer",
    "content": "我们团队主要使用Git进行版本控制，使用GitLab进行代码审查，团队规模约12人，分布在3个时区",
    "sessionId": "session_1703123456_abc123def"
  }
}
```

**确认继续：**
```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "confirm",
    "content": "",
    "sessionId": "session_1703123456_abc123def"
  }
}
```

#### 步骤5：总结生成

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "start",
    "content": "",
    "sessionId": "session_1703123456_abc123def"
  }
}
```

**响应：**
```json
{
  "success": true,
  "message": "**完成！以下是3个总结：**\n\n**1. 执行总结**\n针对\"技术团队远程协作效率和质量问题\"，我们通过Problem Analyst、Solution Architect、Implementation Specialist、Technical Expert等专业角色的协作，完成了系统性的问题分析和解决方案设计。主要成果包括问题深度分析、可行性方案设计和实施路径规划。\n\n**2. 技术总结**\n从技术角度分析，该问题涉及多个技术层面的考虑。通过专业分析，我们识别了关键技术要点和实施难点，并提出了相应的技术解决方案。\n\n**3. 行动建议**\n基于完整的分析和规划，我们建议采取分步骤的行动方案。首先解决最关键的问题，然后逐步推进其他方面的改进。\n\n您对这些总结满意吗？如不满意，我可以重新生成。",
  "data": {
    "summaries": [...]
  },
  "requiresConfirmation": true,
  "sessionId": "session_1703123456_abc123def"
}
```

### 示例2：业务流程优化

#### 快速开始

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "start",
    "content": "我们公司的客户服务流程效率低下，客户投诉处理时间长，客户满意度持续下降，需要重新设计整个客户服务体系。"
  }
}
```

#### 调整问题重述

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "adjust",
    "content": "我更关注在线客户服务，特别是技术支持和售后服务的流程优化",
    "sessionId": "session_1703123457_def456ghi"
  }
}
```

#### 调整研究计划

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "adjust",
    "content": "希望计划中能包含客户满意度调研和竞品分析",
    "sessionId": "session_1703123457_def456ghi"
  }
}
```

#### 重新生成总结

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "regenerate",
    "content": "请更加关注数字化转型和自动化解决方案",
    "sessionId": "session_1703123457_def456ghi"
  }
}
```

## 辅助工具示例

### 获取会话状态

```json
{
  "tool": "get_session_status",
  "arguments": {
    "sessionId": "session_1703123456_abc123def"
  }
}
```

**响应：**
```json
{
  "success": true,
  "message": "当前状态：计划执行与确认\n●●●●○ (4/5)\n\n我将逐步执行计划，关键步骤会请您确认结果。",
  "data": {
    "currentStep": 4,
    "stepName": "计划执行与确认",
    "isComplete": false,
    "problem": "我们的技术团队在远程协作中遇到了严重问题..."
  },
  "sessionId": "session_1703123456_abc123def"
}
```

### 重置到指定步骤

```json
{
  "tool": "reset_to_step",
  "arguments": {
    "sessionId": "session_1703123456_abc123def",
    "step": 2
  }
}
```

### 获取帮助

```json
{
  "tool": "get_help",
  "arguments": {}
}
```

### 获取统计信息

```json
{
  "tool": "get_stats",
  "arguments": {}
}
```

## 错误处理示例

### 会话未找到

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "confirm",
    "content": "",
    "sessionId": "invalid_session_id"
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "会话未找到，请重新开始。使用 action: 'start' 开始新的问题解决流程。",
  "sessionId": "invalid_session_id"
}
```

### 输入验证失败

```json
{
  "tool": "problem_solver",
  "arguments": {
    "action": "start",
    "content": ""
  }
}
```

**错误响应：**
```json
{
  "success": false,
  "message": "请提供有效的问题描述：\n错误：问题描述不能为空",
  "sessionId": ""
}
```

## 最佳实践

1. **保存会话ID**: 始终保存并使用正确的sessionId
2. **检查响应状态**: 每次都检查success字段
3. **处理确认流程**: 正确处理requiresConfirmation标志
4. **提供用户反馈**: 显示进度和状态信息
5. **错误恢复**: 实现适当的错误处理和重试机制
